package com.imile.attendance.deviceConfig.impl;

import com.imile.attendance.deviceConfig.AttendanceGpsConfigManage;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceGpsConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
@Component
public class AttendanceGpsConfigManageImpl implements AttendanceGpsConfigManage {

    @Resource
    private AttendanceGpsConfigDao attendanceGpsConfigDao;

    @Override
    public List<AttendanceGpsConfigDO> getAllGpsConfig() {
        List<AttendanceGpsConfigDO> list = attendanceGpsConfigDao.list(new AttendanceGpsConfigQuery());
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }
}

package com.imile.attendance.deviceConfig;

import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
public interface AttendanceMobileConfigManage {


    /**
     * 用户考勤手机查询
     */
    List<AttendanceMobileConfigDO> queryMobileConfigByUserCode(String userCode);

    /**
     * 用户考勤手机历史记录查询
     */
    List<AttendanceMobileConfigDO> queryHistoryMobileConfigByUserCode(String userCode);
}

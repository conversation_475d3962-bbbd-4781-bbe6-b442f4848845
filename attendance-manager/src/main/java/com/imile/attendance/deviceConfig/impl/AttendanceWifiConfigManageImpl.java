package com.imile.attendance.deviceConfig.impl;

import com.imile.attendance.deviceConfig.AttendanceWifiConfigManage;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceWifiConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
@Component
public class AttendanceWifiConfigManageImpl implements AttendanceWifiConfigManage {

    @Resource
    private AttendanceWifiConfigDao attendanceWifiConfigDao;

    @Override
    public List<AttendanceWifiConfigDO> getAllWifiConfig() {
        List<AttendanceWifiConfigDO> list = attendanceWifiConfigDao.list(new AttendanceWifiConfigQuery());
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }
}

package com.imile.attendance.deviceConfig.impl;

import com.imile.attendance.deviceConfig.AttendanceMobileConfigManage;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceMobileConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceMobileConfigMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
@Component
public class AttendanceMobileConfigManageImpl implements AttendanceMobileConfigManage {

    @Resource
    private AttendanceMobileConfigDao attendanceMobileConfigDao;


    @Override
    public List<AttendanceMobileConfigDO> queryMobileConfigByUserCode(String userCode) {
        return attendanceMobileConfigDao.queryAttendanceMobileConfigByUserCode(userCode);
    }

    @Override
    public List<AttendanceMobileConfigDO> queryHistoryMobileConfigByUserCode(String userCode) {
        return attendanceMobileConfigDao.queryHistoryMobileConfigByUserCode(userCode);
    }
}

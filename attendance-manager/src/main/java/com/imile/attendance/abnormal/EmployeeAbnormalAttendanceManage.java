package com.imile.attendance.abnormal;

import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class EmployeeAbnormalAttendanceManage {

    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;

    public Map<Long, List<EmployeeAbnormalAttendanceDO>> mapByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList)
                .stream()
                .filter(item -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus()))
                .collect(Collectors.toList());
        return abnormalAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getUserId));
    }

    /**
     * 查询用户当前考勤日的所有异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdAndDayId(Long userId, Long dayId) {
        return employeeAbnormalAttendanceDao.selectAbnormalByUserIdAndDayId(userId, dayId);
    }


    /**
     * 查询用户在一段时间内的异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserId(Long userId, Long startDayId, Long endDayId) {
        return employeeAbnormalAttendanceDao.selectAbnormalByUserId(userId, startDayId, endDayId);
    }

    /**
     * 查询用户指定天的异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalAttendanceByDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        return employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList);
    }

    public List<EmployeeAbnormalAttendanceDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return employeeAbnormalAttendanceDao.listByIds(idList);
    }

    /**
     * 查询用户的所有异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdList(List<Long> userIdList){
        return employeeAbnormalAttendanceDao.selectAbnormalByUserIdList(userIdList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalFormConfirmCycle(List<AttendanceFormDO> updateFormList,
                                               List<AttendanceFormAttrDO> addAttrList,
                                               List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                               List<EmployeeAbnormalOperationRecordDO> addRecordList) {
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addAttrList)) {
            attendanceFormAttrDao.saveBatch(addAttrList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            employeeAbnormalAttendanceDao.updateBatchById(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(addRecordList)) {
            employeeAbnormalOperationRecordDao.saveBatch(addRecordList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void abnormalConfirmSave(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList,
                                    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList) {
        if (CollectionUtils.isNotEmpty(abnormalAttendanceDOList)) {
            employeeAbnormalAttendanceDao.updateBatchById(abnormalAttendanceDOList);
        }
        if (CollectionUtils.isNotEmpty(abnormalOperationRecordDOList)) {
            employeeAbnormalOperationRecordDao.saveBatch(abnormalOperationRecordDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void abnormalOffSave(AttendanceEmployeeDetailDO employeeDetailDO,
                                EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO,
                                List<AttendanceEmployeeDetailDO> existPresentEmployeeDetailDOList) {
        if (employeeDetailDO != null) {
            attendanceEmployeeDetailDao.save(employeeDetailDO);
        }
        if (abnormalAttendanceDO != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (abnormalOperationRecordDO != null) {
            employeeAbnormalOperationRecordDao.save(abnormalOperationRecordDO);
        }
        if (CollectionUtils.isNotEmpty(existPresentEmployeeDetailDOList)) {
            attendanceEmployeeDetailDao.updateBatchById(existPresentEmployeeDetailDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAbnormalPresentUpdate(List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                           List<AttendanceFormDO> updateFormList,
                                           List<AttendanceEmployeeDetailDO> addEmployeeDetailList,
                                           List<EmployeeAbnormalOperationRecordDO> addOperationList,
                                           List<AttendanceEmployeeDetailDO> updateEmployeeDetailList) {
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            employeeAbnormalAttendanceDao.updateBatchById(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addEmployeeDetailList)) {
            attendanceEmployeeDetailDao.saveBatch(addEmployeeDetailList);
        }
        if (CollectionUtils.isNotEmpty(addOperationList)) {
            employeeAbnormalOperationRecordDao.saveBatch(addOperationList);
        }
        if (CollectionUtils.isNotEmpty(updateEmployeeDetailList)) {
            attendanceEmployeeDetailDao.updateBatchById(updateEmployeeDetailList);
        }
    }


}

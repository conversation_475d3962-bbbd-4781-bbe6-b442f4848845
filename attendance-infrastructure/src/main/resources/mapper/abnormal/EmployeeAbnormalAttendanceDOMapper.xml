<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalAttendanceMapper">

    <!--员工出勤异常列表查询-->
    <select id="list"
            parameterType="com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery"
            resultType="com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO">
        SELECT
        eaa.id as id,
        hui.id as userId,
        hui.user_code as userCode,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.email as email,
        hui.profile_photo_url as profilePhotoUrl,
        hui.location_country as country,
        eaa.dept_id as deptId,
        eaa.post_id as postId,
        eaa.date as date,
        eaa.day_id as dayId,
        eaa.staff_type as staffType,
        eaa.employee_type as employeetype,
        eaa.abnormal_type as abnormalType,
        eaa.status as status,
        eaa.attendance_type as attendanceType,
        eaa.punch_config_id as punchConfigId,
        eaa.punch_class_config_id as punchClassConfigId,
        eaa.punch_class_item_config_id as punchClassItemConfigId,
        eaa.last_upd_date as lastUpdDate,
        eaa.last_upd_user_name as lastUpdUserName
        from employee_abnormal_attendance eaa
        inner join user_info hui on eaa.user_id = hui.id and eaa.is_delete = 0
        <if test="startDate!=null">
            and eaa.date >= #{startDate}
        </if>
        <if test="endDate!=null">
            and eaa.date &lt;#{endDate}
        </if>
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join calendar_config_range as ccr on ccr.biz_id = hui.id and ccr.is_delete=0 and
            ccr.is_latest=1
            inner join calendar_config as cc on cc.id = ccr.attendance_config_id and cc.is_delete=0 and
            cc.is_latest=1 and cc.attendance_config_no = #{attendanceConfigNo}
        </if>
        <where>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>

            <if test="abnormalIds!=null and abnormalIds.size()>0">
                <foreach collection="abnormalIds" item="abnormalId" open="and eaa.id in (" close=")"
                         separator=",">
                    #{abnormalId}
                </foreach>
            </if>
            <if test="locationCountry!=null and locationCountry!=''">
                and hui.location_country = #{locationCountry}
            </if>
            <if test="employeeTypeList!=null and employeeTypeList.size()>0">
                <foreach collection="employeeTypeList" item="employeeType" open="and eaa.employee_type in (" close=")"
                         separator=",">
                    #{employeeType}
                </foreach>
            </if>
            <if test="statusList!=null and statusList.size()>0">
                <foreach collection="statusList" separator="," open="and eaa.status in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="staffTypes!=null and staffTypes.size()>0">
                <foreach collection="staffTypes" separator="," open="and eaa.staff_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="abnormalTypeList!=null and abnormalTypeList.size()>0">
                <foreach collection="abnormalTypeList" separator="," open="and eaa.abnormal_type in (" close=")"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" item="userId" open="and eaa.user_id in (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="dayId!=null and dayId!=''">
                and eaa.day_id = #{dayId}
            </if>
            <include refid="permissionCountryAndDeptConditions"/>

        </where>
        order by eaa.day_id desc, eaa.id desc
    </select>


    <!-- 权限和部门条件片段 -->
    <sql id="permissionCountryAndDeptConditions">
        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
            <if test="deptIds!=null and deptIds.size()>0">
                and (hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    or hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="("
                             close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
                )
            </if>

            <if test="deptIds==null or deptIds.size()==0">
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    and hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="("
                             close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
            </if>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
            and hui.dept_id in
            <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
            and hui.location_country in
            <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                     separator=",">
                #{locationCountry}
            </foreach>
        </if>

        <if test="isChooseDept!=null and isChooseDept == true">
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                    #{deptId}
                </foreach>
            </if>
        </if>
    </sql>


    <!--员工出勤异常列表查询-->
    <select id="listCount"
            parameterType="com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery"
            resultType="java.lang.Integer">
        SELECT
        count(eaa.id)
        from employee_abnormal_attendance eaa
        inner join user_info hui on eaa.user_id = hui.id and eaa.is_delete = 0
        <if test="startDate!=null">
            and eaa.date >= #{startDate}
        </if>
        <if test="endDate!=null">
            and eaa.date &lt;#{endDate}
        </if>
        <where>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>
            <if test="locationCountry!=null">
                and hui.location_country = #{locationCountry}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and eaa.employee_type = #{employeeType}
            </if>
            <if test="status!=null and status!=''">
                and eaa.status = #{status}
            </if>
            <if test="staffType!=null and staffType!=''">
                and eaa.staff_type = #{staffType}
            </if>
            <if test="staffTypes!=null and staffTypes.size()>0">
                <foreach collection="staffTypes" separator="," open="and eaa.staff_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="abnormalType!=null and abnormalType!=''">
                and eaa.abnormal_type = #{abnormalType}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" separator="," open="and eaa.user_id in (" close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="statusList!=null and statusList.size()>0">
                <foreach collection="statusList" separator="," open="and eaa.status in (" close=")" item="status">
                    #{status}
                </foreach>
            </if>
            <include refid="permissionCountryAndDeptConditions"/>
        </where>
    </select>

</mapper>

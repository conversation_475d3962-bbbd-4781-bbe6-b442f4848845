package com.imile.attendance.infrastructure.notice;

import com.imile.attendance.enums.BizTypeEnum;
import com.imile.attendance.enums.PlatFormTypeEnum;
import com.imile.attendance.hrms.support.RpcPlatformRelationClientSupport;
import com.imile.attendance.infrastructure.notice.dto.UserWxInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.hrms.api.platform.dto.PlatformRelationApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/29 
 * @Description
 */
@Slf4j
@Service
public class WechatUserApiService {

    @Resource
    private RpcPlatformRelationClientSupport platformRelationClientSupport;
    @Resource
    private UserInfoDao userInfoDao;


    /**
     * 批量获取用户微信信息
     */
    public List<UserWxInfoDTO> batchGetUserWxIds(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        // 获取用户微信ID映射关系
        List<PlatformRelationApiDTO> platformRelations = platformRelationClientSupport.listPlatFormRelation(
                userCodes,
                BizTypeEnum.USER.name(),
                null,
                PlatFormTypeEnum.WECHAT_WORK.name()
        );

        if (CollectionUtils.isEmpty(platformRelations)) {
            log.info("batchGetUserWxIds: 未找到用户微信关联关系, userCodes:{}", userCodes);
            return Collections.emptyList();
        }

        // 构建用户编码到微信ID的映射
        Map<String, String> userCodeToWxIdMap = platformRelations.stream()
                .collect(Collectors.toMap(
                        PlatformRelationApiDTO::getBizId,
                        PlatformRelationApiDTO::getRelationId,
                        (existing, replacement) -> existing
                ));

        // 获取用户基本信息
        List<UserInfoDO> userInfoList = userInfoDao.listByUserCodes(userCodes);
        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("batchGetUserWxIds: 未找到用户信息, userCodes:{}", userCodes);
            return Collections.emptyList();
        }
        return userInfoList.stream()
                .filter(userInfo -> userCodeToWxIdMap.containsKey(userInfo.getUserCode()))
                .map(userInfo -> {
                    UserWxInfoDTO userWxInfo = new UserWxInfoDTO();
                    userWxInfo.setUserCode(userInfo.getUserCode());
                    userWxInfo.setWxId(userCodeToWxIdMap.get(userInfo.getUserCode()));
                    return userWxInfo;
                })
                .collect(Collectors.toList());
    }
}

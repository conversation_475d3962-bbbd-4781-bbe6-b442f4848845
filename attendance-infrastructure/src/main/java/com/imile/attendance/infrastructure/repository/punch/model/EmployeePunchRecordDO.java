package com.imile.attendance.infrastructure.repository.punch.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *  员工打卡记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("employee_punch_record")
public class EmployeePunchRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 数据来源 司机打卡：driver 微信：wechat
     */
    @ApiModelProperty(value = "数据来源")
    private String sourceType;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    private String userCode;

    /**
     * 打卡时间
     */
    @ApiModelProperty(value = "打卡时间")
    private Date punchTime;

    /**
     * 打卡区域
     */
    @ApiModelProperty(value = "打卡区域")
    private String punchArea;

    /**
     * 关联id 中控考勤打卡id
     */
    @ApiModelProperty(value = "关联id")
    private Integer relationId;

    /**
     * 打卡方式
     */
    @ApiModelProperty(value = "打卡方式")
    private String punchCardType;

    /**
     * dayid 示例：20220124
     */
    @ApiModelProperty(value = "dayId")
    private String dayId;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段")
    private String extend;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private BigDecimal orderby;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 网点编码
     */
    @ApiModelProperty(value = "网点编码")
    private String ocCode;
    /**
     * 网点名称
     */
    @ApiModelProperty(value = "网点名称")
    private String ocName;
    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long deptId;
    /**
     * 员工类型
     */
    @ApiModelProperty(value = "员工类型")
    private String employeeType;
    /**
     * 上班/下班
     */
    @ApiModelProperty(value = "上班/下班")
    private String punchType;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private BigDecimal ocLongitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private BigDecimal ocLatitude;
    /**
     * 两点之间距离计算
     */
    @ApiModelProperty(value = "两点之间距离计算")
    private BigDecimal distance;

    /**
     * 审批单ID
     */
    @ApiModelProperty(value = "审批单ID")
    private Long formId;

    /**
     * 打卡设备ID
     */
    @ApiModelProperty(value = "打卡设备ID")
    private Long mobileConfigId;

    /**
     * wifi配置ID
     */
    @ApiModelProperty(value = "wifi配置ID")
    private Long wifiConfigId;

    /**
     * gps配置ID
     */
    @ApiModelProperty(value = "gps配置ID")
    private Long gpsConfigId;

    /**
     * wifi名称
     */
    @ApiModelProperty(value = "wifi名称")
    private String wifiConfigName;

    /**
     * gps地址名称
     */
    @ApiModelProperty(value = "gps地址名称")
    private String gpsConfigName;

    /**
     * 班次id
     */
    @ApiModelProperty(value = "班次id")
    private Long classId;

    /**
     * 客户端IP
     */
    @ApiModelProperty(value = "客户端IP")
    private String ip;
}

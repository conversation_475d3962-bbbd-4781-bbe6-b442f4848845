package com.imile.attendance.hrms.impl;

import com.imile.attendance.hrms.RpcPlatformRelationClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.platform.PlatformRelationApiQuery;
import com.imile.hrms.api.platform.api.PlatformRelationApi;
import com.imile.hrms.api.platform.dto.PlatformRelationApiDTO;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@RequiredArgsConstructor
@Component
public class RpcPlatformRelationClientImpl implements RpcPlatformRelationClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private PlatformRelationApi platformRelationApi;

    @Override
    public List<PlatformRelationApiDTO> listPlatFormRelation(PlatformRelationApiQuery query) {
        return RpcResultProcessor.process(platformRelationApi.listPlatFormRelation(query));
    }
}

package com.imile.attendance.controller.punch;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.punch.dto.UserPunchListExportDTO;
import com.imile.attendance.punch.query.PunchCardRecordQuery;
import com.imile.attendance.punch.dto.PunchCardRecordResultDTO;
import com.imile.attendance.punch.query.EmployeePunchCardExportQuery;
import com.imile.attendance.punch.vo.EmployeePunchRecordListExportVO;
import com.imile.attendance.punch.vo.PunchCardRecordResultVO;
import com.imile.attendance.punch.vo.PunchCardRecordVO;
import com.imile.attendance.punch.vo.UserPunchExportListVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @menu 打卡功能记录
 * @email <EMAIL>
 * @date 2022/2/14
 */

@Slf4j
@RestController
@RequestMapping("/punch/record/card")
public class EmployeePunchCardController extends BaseController {

    @Resource
    private EmployeePunchRecordService employeePunchRecordService;

    @Resource
    private ConverterService converterService;

    @PostMapping("/punchRecord")
    public Result<PaginationResult<PunchCardRecordResultVO>> listPunchCardRecord(EmployeePunchCardExportQuery param,
                                                                                 HttpServletRequest request) {
        this.setExcelCallBackParam(request, param);
        PunchCardRecordQuery query = PunchCardRecordQuery.builder()
                .country(param.getCountry())
                .startDate(param.getStartDate())
                .endDate(param.getEndDate())
                .build();
        PaginationResult<PunchCardRecordResultDTO> listPunchList = employeePunchRecordService.listPunchCardRecord(query);
        PaginationResult<PunchCardRecordResultVO> voPaginationResult = this.convertPage(listPunchList, PunchCardRecordResultVO.class);
        converterService.withAnnotation(voPaginationResult.getResults());
        return Result.ok(voPaginationResult);
    }

    /**
     * 打卡记录列表
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    public Result<PaginationResult<PunchCardRecordVO>> list(@RequestBody UserPunchCardRecordQuery query) {
        PaginationResult<PunchCardRecordDTO> result = employeePunchRecordService.listRecord(query);
        PaginationResult<PunchCardRecordVO> voPaginationResult = this.convertPage(result, PunchCardRecordVO.class);
        converterService.withAnnotation(voPaginationResult.getResults());
        return Result.ok(voPaginationResult);
    }

    /**
     * 打卡记录统计导出
     *
     * @param query
     * @return
     */
    @PostMapping("list/export")
    public Result<PaginationResult<UserPunchExportListVO>> listExport(HttpServletRequest request, UserPunchCardRecordQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<UserPunchListExportDTO> res = employeePunchRecordService.export(query);
        PaginationResult<UserPunchExportListVO> resultVO = this.convertPage(res, UserPunchExportListVO.class);
        List<UserPunchExportListVO> userPunchExportListVOS = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(userPunchExportListVOS);
        return Result.ok(resultVO);
    }

    /**
     * 打卡记录导出
     *
     * @param query
     * @return
     */
    @PostMapping("list/record/export")
    public Result<PaginationResult<EmployeePunchRecordListExportVO>> punchRecordListExport(HttpServletRequest request,
                                                                                           UserPunchCardRecordQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<PunchCardRecordDTO> res = employeePunchRecordService.punchRecordListExport(query);
        PaginationResult<EmployeePunchRecordListExportVO> resultVO = this.convertPage(res, EmployeePunchRecordListExportVO.class);
        List<EmployeePunchRecordListExportVO> employeePunchRecordListExportVOList = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(employeePunchRecordListExportVOList);
        return Result.ok(resultVO);
    }

}

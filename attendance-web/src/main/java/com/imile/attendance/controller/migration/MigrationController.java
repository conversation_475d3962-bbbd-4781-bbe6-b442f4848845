package com.imile.attendance.controller.migration;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.migration.MigrationService;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统迁移相关接口
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description 提供系统迁移相关的HTTP接口
 */
@RequestMapping("/migration")
@RestController
public class MigrationController {

    @Resource
    private MigrationService migrationService;

    /**
     * 获取启用新考勤系统的国家列表
     * @return 国家列表
     */
    @GetMapping("/getEnableNewAttendanceCountry")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<List<String>> getEnableNewAttendanceCountry() {
        return Result.ok(migrationService.getEnableNewAttendanceCountry());
    }

    /**
     * 获取当前用户编码
     * @return 用户编码
     */
    @GetMapping("/getCurrentUserCode")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<String> getCurrentUserCode() {
        return Result.ok(migrationService.getCurrentUserCode());
    }

    /**
     * 验证用户是否启用新考勤系统
     * @return 验证结果
     */
    @GetMapping("/verifyUserIsEnableNewAttendance")
    @NoLoginAuthRequired
    @NoAuthRequired
    @NoLoginRequired
    public Result<Boolean> verifyUserIsEnableNewAttendance() {
        return Result.ok(migrationService.verifyUserIsEnableNewAttendance());
    }
}

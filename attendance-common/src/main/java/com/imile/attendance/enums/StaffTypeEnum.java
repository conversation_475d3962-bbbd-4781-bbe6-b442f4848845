package com.imile.attendance.enums;

import lombok.Getter;

@Getter
public enum StaffTypeEnum {

    ALL("all", "全部员工"),
    DRIVER("driver", "司机"),
    WAREHOUSE("warehouse", "仓内员工"),
    OFFICE("office", "非作业员工");


    private String code;

    private String desc;

    private String descEn;

    StaffTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

package com.imile.attendance.enums;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/23
 */
public enum PlatFormTypeEnum {

    /**
     * 钉钉
     */
    DING_TALK,

    /**
     * 企业微信
     */
    WECHAT_WORK,

    /**
     * hermes
     */
    HERMES,

    /**
     * 司机等级
     */
    DRIVER_LEVEL,

    /**
     * 飞书
     */
    FEI_SHU
    ;

    public static PlatFormTypeEnum getInstance(String platFormType) {
        for (PlatFormTypeEnum value : PlatFormTypeEnum.values()) {
            if (value.name().equals(platFormType)) {
                return value;
            }
        }
        return null;
    }
}

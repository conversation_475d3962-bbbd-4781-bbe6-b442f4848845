package com.imile.attendance.enums;

import com.imile.attendance.context.RequestInfoHolder;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Getter
public enum DeptLeaderPropertyEnum {
    /**
     * 业务负责人类型枚举类
     */
    UNKNOWN(0, "未知", "unknown"),
    HR_RESPONSIBLE(1, "HR接口人", "HR Responsible"),
    PERFORMANCE_AGENT(2, "绩效代理人", "Performance Agent"),
    GM(3, "GM", "GM"),
    HRM(4, "HRM", "HRM"),
    RECRUITER(5, "招聘专员(本地)", "RecruiterLocal"),
    ER(6, "ER", "ER"),
    HRBP(7, "部门HRBP", "Department HRBP"),
    ADMIN(8, "行政主管", "Admin"),
    PAYROLL(9, "部门Payroll", "Payroll"),

    TRAFFIC_COMMISSIONER(101, "交通专员", "AdminTravel"),
    HOTEL_COMMISSIONER(102, "酒店专员", "AdminHotelVisa"),
    VISA_COMMISSIONER(103, "签证专员", "AdminVisa"),
    RECRUITMENT_COMMISSIONER(104, "招聘专员(中方)", "RecruiterCHN"),
    ;

    private final Integer type;

    private final String descCn;

    private final String descEn;

    DeptLeaderPropertyEnum(Integer type, String descCn, String descEn) {
        this.type = type;
        this.descCn = descCn;
        this.descEn = descEn;
    }

    public static DeptLeaderPropertyEnum valueOfType(Integer type) {
        return Arrays.stream(values())
                .filter(item -> item.getType().equals(type))
                .findAny()
                .orElse(DeptLeaderPropertyEnum.UNKNOWN);
    }

    public String getDesc() {
        return RequestInfoHolder.isChinese() ? this.descCn : this.descEn;
    }

    /**
     * 获取HR类型 HRM,HRBP
     * @return List<DeptLeaderPropertyEnum>
     */
    public static List<DeptLeaderPropertyEnum> getHRTypes() {
        return Arrays.asList(DeptLeaderPropertyEnum.HRM, DeptLeaderPropertyEnum.HRBP);
    }
}

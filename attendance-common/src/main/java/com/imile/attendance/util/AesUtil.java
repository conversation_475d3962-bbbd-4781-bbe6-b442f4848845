package com.imile.attendance.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES 加密解密工具类
 * 使用 AES/CBC/PKCS5Padding 模式进行加密解密
 * 支持密钥和 IV 的生成与管理
 */
@Slf4j
public class AesUtil {

    /**
     * AES 密钥，Base64 编码格式，需要与前端保持一致
     */
    public static final String SECRET_KEY_STRING = "DpZtwmIMtUYgJj6px7Cshnij+ut5lZw6ALYhUkrOr3M=";
    /**
     * 初始化向量，Base64 编码格式
     */
    private static String IV_STRING = "3LuCOWbXVa9OqjTON5tsjw==";

    /**
     * AES 加密方法
     * 使用 CBC 模式和 PKCS5Padding 填充
     *
     * @param plainText 待加密的明文字符串
     * @param keyString Base64 编码的密钥
     * @param ivString  Base64 编码的初始化向量
     * @return Base64 编码的加密结果
     * @throws Exception 加密过程中的异常
     */
    public static String encrypt(String plainText, String keyString, String ivString) throws Exception {
        // 1. 将 Base64 编码的字符串解码为字节数组
        byte[] keyBytes = Base64.getDecoder().decode(keyString);
        byte[] ivBytes = Base64.getDecoder().decode(ivString);

        // 2. 从字节数组创建 SecretKeySpec 和 IvParameterSpec 对象
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

        // 3. 使用密钥和 IV 进行加密
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

        // 4. 将加密后的数据进行 Base64 编码
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * AES 解密方法
     * 使用 CBC 模式和 PKCS5Padding 填充
     *
     * @param encryptedText Base64 编码的密文
     * @param keyString     Base64 编码的密钥
     * @param ivString      Base64 编码的初始化向量
     * @return 解密后的明文字符串
     * @throws RuntimeException 解密失败时抛出
     */
    public static String decrypt(String encryptedText, String keyString, String ivString) {
        // 1. 将 Base64 编码的字符串解码为字节数组
        try {
            byte[] keyBytes = Base64.getDecoder().decode(keyString);
            byte[] ivBytes = Base64.getDecoder().decode(ivString);

            // 2. 从字节数组创建 SecretKeySpec 和 IvParameterSpec 对象
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

            // 3. 使用密钥和 IV 进行解密
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);

            // 4. 返回解密后的字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("decrypt解密错误", e);
            throw new RuntimeException("decrypt解密错误", e);
        }
    }

    /**
     * 生成 AES-256 密钥
     * 使用 SecureRandom 生成随机密钥
     *
     * @return Base64 编码的 AES-256 密钥
     * @throws RuntimeException 密钥生成失败时抛出
     */
    public static String genAESKey() {
        // 生成 AES 256 位密钥
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(256, new SecureRandom());
            byte[] keyBytes = keyGenerator.generateKey().getEncoded();
            return Base64.getEncoder().encodeToString(keyBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("生成 AES 密钥错误", e);
            throw new RuntimeException("生成 AES 密钥错误", e);
        }
    }

    /**
     * 生成随机初始化向量（IV）
     * 生成 16 字节（128 位）的随机 IV
     *
     * @return Base64 编码的 IV
     */
    public static String genBase64IV() {
        // 生成 16 字节 (128 位) 随机 IV
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16];
        secureRandom.nextBytes(iv);
        return Base64.getEncoder().encodeToString(iv);
    }

    //    public static void main(String[] args) throws Exception {
////        System.out.println(genAESKey());
////        System.out.println(genBase64IV());
//        String str = new JSONObject()
//                .fluentPut("longitude", 119.99296569824219)
//                .fluentPut("latitude", 30.2767391204834)
//                .fluentPut("gpsConfigId", 1915709370982576129L)
//                .fluentPut("gpsConfigName", "浙富西溪堂2")
//                .fluentPut("gpsConfigCity", "杭州市")
//                .fluentPut("mobileUnicode", "add001c81f9ac8ba")
//                .fluentPut("mobileModel", "iPhone")
//                .fluentPut("mobileBranch", "Apple")
//                .fluentPut("mobileVersion", "iOS 18.1.1")
//                .fluentPut("dayId", 20250527)
//                .fluentPut("userId", 1351186523562856448L)
//                .fluentPut("dateTime", "2025-05-27 09:28:58")
//                .fluentPut("currentTimeStamp", 1748309338000L)
//                .fluentPut("ip", "240e:471:800:dba:809:2367:f0e9:b928")
//                .toJSONString();
//        for (int i = 0; i < 20; i++) {
//            IV_STRING = genBase64IV();
//            String encrypt = encrypt(str, SECRET_KEY_STRING, IV_STRING);
//            System.out.println(encrypt);
//            String decrypt = decrypt(encrypt, SECRET_KEY_STRING, IV_STRING);
//            System.out.println(decrypt);
//            System.out.println(" ");
//        }
//    }
    public static void main(String[] args) throws Exception {
//        System.out.println(genAESKey());
//        System.out.println(genBase64IV());
        String str = new JSONObject()
                .fluentPut("longitude", 119.99296569824219)
                .fluentPut("latitude", 30.2767391204834)
                .fluentPut("gpsConfigId", 1915709370982576129L)
                .fluentPut("gpsConfigName", "浙富西溪堂2")
                .fluentPut("gpsConfigCity", "杭州市")
                .fluentPut("mobileUnicode", "add001c81f9ac8ba")
                .fluentPut("mobileModel", "iPhone")
                .fluentPut("mobileBranch", "Apple")
                .fluentPut("mobileVersion", "iOS 18.1.1")
                .fluentPut("dayId", 20250602)
                .fluentPut("userId", 892057521915826176L)
                .fluentPut("dateTime", "2025-06-02 09:11:58")
                .fluentPut("currentTimeStamp", 1748826658000L)
                .fluentPut("ip", "240e:471:800:dba:809:2367:f0e9:b928")
                .toJSONString();
//        for (int i = 0; i < 20; i++) {
//
//            System.out.println(" ");
//        }
//        IV_STRING = genBase64IV();
        String encrypt = encrypt(str, SECRET_KEY_STRING, IV_STRING);
        System.out.println(encrypt);
        String decrypt = decrypt(encrypt, SECRET_KEY_STRING, IV_STRING);
        System.out.println(decrypt);
    }
}

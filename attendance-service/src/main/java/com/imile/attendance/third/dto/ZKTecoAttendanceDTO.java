package com.imile.attendance.third.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ZKTecoAttendanceDTO implements Serializable {
    private static final long serialVersionUID = -4829115621746764329L;

    private Integer id;

    private String emp;
    /**
     * 员工编码
     */
    private String emp_code;
    /**
     * 名
     */
    private String first_name;
    /**
     * 姓
     */
    private String last_name;
    /**
     * 部门
     */
    private String department;
    /**
     * 岗位
     */
    private String position;
    /**
     * 打卡时间
     */
    private String punch_time;
    /**
     * 打卡状态
     */
    private Integer punch_state;
    /**
     * 打卡状态解释
     */
    private String punch_state_display;
    /**
     * 校验类型
     */
    private Integer verify_type;
    /**
     * 校验类型解释
     */
    private String verify_type_display;

    private String work_code;
    /**
     * gps定位
     */
    private String gps_location;
    /**
     * 区域别名
     */
    private String area_alias;
    /**
     * 终端sn
     */
    private String terminal_sn;
    /**
     * 温度
     */
    private Integer temperature;

    private String is_mask;
    /**
     * 终端别名
     */
    private String terminal_alias;
    /**
     * 上传时间
     */
    private String upload_time;
}

package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.vacation.DispatchUserRecordService;
import com.imile.hrms.api.user.param.UserEventParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/5/24
 * @Description 监听用户派遣消息通知
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.transform.group}",
        selectorExpression = "${rocket.mq.hr.transform.tag}",
        consumeThreadMax = 4)
public class HrTransformListener extends BaseRocketMQListener {

    @Resource
    private DispatchUserRecordService dispatchUserRecordService;

    @Override
    public void doOnMessage(MessageExt messageExt) {
        UserEventParam<?> param = JSON.parseObject(new String(messageExt.getBody()), UserEventParam.class);
        log.info("收到派遣人员变动消息,msgId:{},topic:{},tags:{},param:{}",
                messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));

        UserEventParam.TransformNotice transform = JSON.parseObject(JSON.toJSONString(param.getBody()), UserEventParam.TransformNotice.class);
        dispatchUserRecordService.handleDispatchNotice(transform);
    }
}

package com.imile.attendance.clock.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/23 
 * @Description
 */
@Data
public class PunchClassItemConfigVO implements Serializable {

    private Long id;

    /**
     * 班次规则ID
     */
    private Long punchClassId;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date restEndTime;
}

package com.imile.attendance.clock.application;

import com.imile.attendance.clock.MobilePunchService;
import com.imile.attendance.clock.command.PunchInAddCommand;
import com.imile.attendance.clock.command.PunchInEncryptAddCommand;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.mapstruct.MobileConfigMapstruct;
import com.imile.attendance.clock.query.MobileDayPunchDetailQuery;
import com.imile.attendance.clock.query.MobilePunchDetailQuery;
import com.imile.attendance.clock.vo.UserDayMobilePunchDetailVO;
import com.imile.attendance.clock.vo.UserMobileRuleConfigVO;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceGpsConfigMapstruct;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceWifiConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19
 * @Description
 */
@Slf4j
@Service
public class MobilePunchApplicationService {

    @Resource
    private MobilePunchService mobilePunchService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ConverterService converterService;


    /**
     * 通过经纬度获取时间
     */
    public Date getTimeZone(String lat, String lng) {
        return mobilePunchService.getTimeZone(lat, lng);
    }

    /**
     * 个人考勤详情
     */
    public UserDayMobilePunchDetailVO userDetail(MobilePunchDetailQuery query) {
        UserDayMobilePunchDetailDTO mobilePunchDetailDTO = mobilePunchService.userDetail(query);
        return convertToVOWithDictTranslation(mobilePunchDetailDTO);
    }

    /**
     * 个人指定天的考勤详情
     */
    public UserDayMobilePunchDetailVO userDetailByDayId(MobileDayPunchDetailQuery query){
        UserDayMobilePunchDetailDTO mobilePunchDetailDTO = mobilePunchService.userDetailByDayId(query);
        mobilePunchService.afterPostProcessor(mobilePunchDetailDTO);
        return convertToVOWithDictTranslation(mobilePunchDetailDTO);
    }

    /**
     * 统一转换DTO到VO并进行字典翻译
     * 将UserDayMobilePunchDetailDTO转换为UserDayMobilePunchDetailVO，并对ruleConfigVO进行字典转换
     *
     * @param mobilePunchDetailDTO 移动端打卡详情DTO
     * @return 转换后的VO对象，包含字典翻译后的规则配置信息
     */
    private UserDayMobilePunchDetailVO convertToVOWithDictTranslation(UserDayMobilePunchDetailDTO mobilePunchDetailDTO) {
        UserDayMobilePunchDetailVO mobilePunchDetailVO = MobileConfigMapstruct.INSTANCE.toUserDayMobilePunchDetailVO(mobilePunchDetailDTO);
        UserMobileRuleConfigVO ruleConfigVO = mobilePunchDetailVO.getRuleConfigVO();
        converterService.withAnnotationForSingle(ruleConfigVO);
        return mobilePunchDetailVO;
    }

    /**
     * 移动打卡
     */
    public void punchIn(PunchInEncryptAddCommand punchInEncryptAddCommand) {
        PunchInAddCommand punchInAddCommand = mobilePunchService.punchInDecrypt(punchInEncryptAddCommand);
        long startTime = System.currentTimeMillis();
        log.info("/mobile/punch/punchIn | param:{}", punchInAddCommand);
        log.info("/mobile/punch/punchIn | startTime:{},userCode:{}", startTime, RequestInfoHolder.getUserCode());
        String lockKey = "LOCK:MOBILE_PUNCH_IN_SYNC:" + RequestInfoHolder.getUserCode();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            //上锁，时间为15秒
            boolean tryLock = lock.tryLock(15, TimeUnit.SECONDS);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.CHECK_IN_AGAIN_ERROR.getCode(),
                    ErrorCodeEnum.CHECK_IN_AGAIN_ERROR.getDesc());
            mobilePunchService.punchIn(punchInAddCommand);
        } catch (BusinessException e) {
            log.error("punchIn fail:", e);
            throw e;
        } catch (Exception e) {
            log.error("punchIn fail:", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            if (lock!=null && lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("mobile/punch/punchIn | endTime:{},userCode:{}", endTime, RequestInfoHolder.getUserCode());
        log.info("mobile/punch/punchIn | 耗时:{},userCode:{}", endTime - startTime, RequestInfoHolder.getUserCode());
    }
}

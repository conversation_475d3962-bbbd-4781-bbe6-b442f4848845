package com.imile.attendance.clock.bo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description 用户打卡缺失规则类型
 */
@Getter
@AllArgsConstructor
public enum UserPunchMissingRuleType {

    /**
     * 缺失打卡规则和班次规则
     */
    MISSING_PUNCH_AND_CLASS_RULE("MISSING_PUNCH_AND_CLASS_RULE", "no_class_and_punch_rule_remind_template_cn", "no_class_and_punch_rule_remind_template_en"),
    /**
     * 缺失打卡规则
     */
    MISSING_PUNCH_RULE("MISSING_PUNCH_RULE", "no_punch_rule_template_cn", "no_punch_rule_template_en"),
    /**
     * 缺失班次规则
     */
    MISSING_CLASS_RULE("MISSING_CLASS_RULE", "no_class_rule_template_cn", "no_class_rule_template_en"),
    ;


    private final String code;

    private final String templateCodeCn;

    private final String templateCodeEn;


    private final static Map<String, UserPunchMissingRuleType> mapCache = new HashMap<>();

    static {
        for (UserPunchMissingRuleType value : UserPunchMissingRuleType.values()) {
            mapCache.put(value.getCode(), value);
        }
    }

    public static UserPunchMissingRuleType getByCode(String code) {
        return mapCache.get(code);
    }

    public String getTemplateCodeByCountryCode(String countryCode) {
        if (StringUtils.isNotBlank(countryCode) && "CN".equals(countryCode)) {
            return this.getTemplateCodeCn();
        }
        return this.getTemplateCodeEn();
    }

    public static void main(String[] args) {
        System.out.println(UserPunchMissingRuleType.MISSING_PUNCH_AND_CLASS_RULE.getTemplateCodeByCountryCode("CN"));
        System.out.println(UserPunchMissingRuleType.MISSING_PUNCH_AND_CLASS_RULE.getTemplateCodeByCountryCode("EN"));
    }
}

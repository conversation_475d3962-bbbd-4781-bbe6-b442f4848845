package com.imile.attendance.clock.mapstruct;

import com.imile.attendance.clock.dto.MobileAbnormalDTO;
import com.imile.attendance.clock.dto.MobileFormDTO;
import com.imile.attendance.clock.dto.MobilePunchCardRecordDTO;
import com.imile.attendance.clock.dto.PunchClassConfigDTO;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.dto.UserMobileRuleConfigDTO;
import com.imile.attendance.clock.vo.MobileAbnormalVO;
import com.imile.attendance.clock.vo.MobileFormVO;
import com.imile.attendance.clock.vo.MobilePunchCardRecordVO;
import com.imile.attendance.clock.vo.PunchClassConfigVO;
import com.imile.attendance.clock.vo.PunchClassItemConfigVO;
import com.imile.attendance.clock.vo.UserDayMobilePunchDetailVO;
import com.imile.attendance.clock.vo.UserMobileRuleConfigVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.punch.vo.PunchCardRecordVO;
import com.imile.attendance.util.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface MobileConfigMapstruct {

    MobileConfigMapstruct INSTANCE = Mappers.getMapper(MobileConfigMapstruct.class);


    MobilePunchCardRecordDTO toMobilePunchCardRecordDTO(EmployeePunchRecordDO punchRecordDO);

    List<MobilePunchCardRecordDTO> toMobilePunchCardRecordDTO(List<EmployeePunchRecordDO> punchRecordDOList);


    default MobilePunchCardRecordDTO toMobilePunchCardRecordDTO(EmployeePunchRecordDO punchRecord,
                                                                PunchClassItemConfigDTO classItemConfigDTO) {
        MobilePunchCardRecordDTO dto = toMobilePunchCardRecordDTO(punchRecord);
        dto.setClassId(classItemConfigDTO.getPunchClassId());
        dto.setClassItemId(classItemConfigDTO.getId());
        return dto;
    }


    PunchClassConfigDTO toPunchClassConfigDTO(PunchClassConfigDO punchClassConfigDO);

    List<PunchClassConfigDTO> toPunchClassConfigDTO(List<PunchClassConfigDO> punchClassConfigDOList);


    default UserMobileRuleConfigDTO toRuleConfigDTO(PunchConfigDO userPunchConfigDO, ReissueCardConfigDO userReissueCardConfigDO) {
        UserMobileRuleConfigDTO userMobileRuleConfigDTO = new UserMobileRuleConfigDTO();
        //打卡规则一般不为空
        if (null != userPunchConfigDO) {
            userMobileRuleConfigDTO.setPunchConfigNo(userPunchConfigDO.getConfigNo());
            userMobileRuleConfigDTO.setPunchConfigName(userPunchConfigDO.getConfigName());
            userMobileRuleConfigDTO.setPunchConfigType(userPunchConfigDO.getConfigType());
            userMobileRuleConfigDTO.setPunchTimeInterval(userPunchConfigDO.getPunchTimeInterval());
        }
        if (null != userReissueCardConfigDO) {
            userMobileRuleConfigDTO.setMaxRepunchNumber(userReissueCardConfigDO.getMaxRepunchNumber());
        }
        return userMobileRuleConfigDTO;
    }

    // DTO 到 VO 的映射方法
    UserMobileRuleConfigVO toUserMobileRuleConfigVO(UserMobileRuleConfigDTO dto);

    PunchClassConfigVO toPunchClassConfigVO(PunchClassConfigDTO dto);

    PunchClassItemConfigVO toPunchClassItemConfigVO(PunchClassItemConfigDTO dto);

    List<PunchClassItemConfigVO> toPunchClassItemConfigVO(List<PunchClassItemConfigDTO> dtoList);

    MobilePunchCardRecordVO toMobilePunchCardRecordVO(MobilePunchCardRecordDTO dto);

    List<MobilePunchCardRecordVO> toMobilePunchCardRecordVO(List<MobilePunchCardRecordDTO> dtoList);

    MobileFormVO toMobileFormVO(MobileFormDTO dto);

    List<MobileFormVO> toMobileFormVO(List<MobileFormDTO> dtoList);

    MobileAbnormalVO toMobileAbnormalVO(MobileAbnormalDTO dto);

    List<MobileAbnormalVO> toMobileAbnormalVO(List<MobileAbnormalDTO> dtoList);

    @Mapping(target = "ruleConfigVO", source = "ruleConfigDTO")
    @Mapping(target = "punchClassItemConfigVO", source = "punchClassItemConfigDTO")
    @Mapping(target = "punchClassConfigVO", source = "punchClassConfigDTO")
    @Mapping(target = "punchCardRecordVO", source = "punchCardRecordDTO")
    @Mapping(target = "mobileFormVOList", source = "mobileFormDTOList")
    @Mapping(target = "abnormalPunchList", source = "abnormalPunchList")
    UserDayMobilePunchDetailVO toUserDayMobilePunchDetailVO(UserDayMobilePunchDetailDTO dto);
}

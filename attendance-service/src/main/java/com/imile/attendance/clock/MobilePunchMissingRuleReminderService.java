package com.imile.attendance.clock;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.base.Operator;
import com.imile.attendance.clock.bo.UserPunchMissingRuleType;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.notice.WeChatAppApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description
 */
@Slf4j
@Service
public class MobilePunchMissingRuleReminderService {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private WeChatAppApiService weChatAppApiService;

    @Value("${attendance.env.host:https://dev-attendance-miniapp.52imile.cn}")
    private String attendanceEnvHost;


    /**
     * 缺失打卡规则和班次规则的提醒消息redis key
     */
    private static final String MISSING_RULE_USER_REMINDER_KEY = "ATTENDANCE:MOBILE:PUNCH:MISSING_RULE:REMINDER:USER:";

    /**
     * 固定班次规则配置页面
     */
    private static final String PUNCH_FIXED_CLASS_RULE_CONFIG_SUFFIX = "/attendance-web/#/AttendaceManage/ShiftSetting/FixedShiftSetting";
    /**
     * 多班次规则配置页面
     */
    private static final String PUNCH_MULTIPLE_CLASS_RULE_CONFIG_SUFFIX = "/attendance-web/#/AttendaceManage/ShiftSetting/MultiShiftSetting";
    /**
     * 打卡规则配置页面
     */
    private static final String PUNCH_RULE_CONFIG_SUFFIX = "/attendance-web/#/AttendaceManage/OfficeAttendance/PunchRule";

    /**
     * 发送提醒消息
     */
    public void sendHrReminderMessage(UserDayMobilePunchDetailDTO mobilePunchDetailDTO) {
        if (CollectionUtils.isEmpty(mobilePunchDetailDTO.getSuperiorDeptHrList())) {
            log.info("sendHrReminderMessage | superiorDeptHrList is empty, userId:{}", mobilePunchDetailDTO.getUserId());
            return;
        }
        String userCode = mobilePunchDetailDTO.getUserCode();
        //判断是否需要发送消息
        UserPunchMissingRuleType userPunchMissingRuleType = mobilePunchDetailDTO.getUserPunchMissingRuleType();
        if (null == userPunchMissingRuleType) {
            return;
        }
        //判断消息是否已经发过
        String lockKey = MISSING_RULE_USER_REMINDER_KEY + userCode;
        RBucket<String> bucket = redissonClient.getBucket(lockKey);
        if (bucket.isExists()) {
            log.info("sendHrReminderMessage | userCode:{} already sent reminder message in 24 hours", userCode);
            return;
        }
        //发送消息
        JSONObject templateData = null;
        switch (userPunchMissingRuleType) {
            case MISSING_PUNCH_AND_CLASS_RULE:
                templateData = buildNoPunchAndClassRuleReminderMessage(userCode, mobilePunchDetailDTO.getUserName(), mobilePunchDetailDTO.getClassNature());
                break;
            case MISSING_PUNCH_RULE:
                templateData = buildNoPunchRuleReminderMessage(userCode, mobilePunchDetailDTO.getUserName());
                break;
            case MISSING_CLASS_RULE:
                templateData = buildClassRuleReminderMessage(userCode, mobilePunchDetailDTO.getUserName(), mobilePunchDetailDTO.getClassNature());
                break;
            default:
                break;
        }
        if (templateData == null) {
            log.error("sendHrReminderMessage | userCode:{} not support userPunchMissingRuleType:{}", userCode, userPunchMissingRuleType);
            return;
        }
        Object obj = weChatAppApiService.sendAppMessage(
                mobilePunchDetailDTO.getSuperiorDeptHrList()
                        .stream().map(Operator::getId)
                        .collect(Collectors.toList()),
                true,
                userPunchMissingRuleType.getTemplateCodeByCountryCode(mobilePunchDetailDTO.getCountryCode()),
                templateData
        );
        if (obj == null) {
            log.error("sendHrReminderMessage | userCode:{} send reminder message fail", userCode);
            return;
        }
        log.info("sendHrReminderMessage | userCode:{} send reminder message success", userCode);
        //设置24小时过期
        bucket.set(userCode, 24, TimeUnit.HOURS);
    }


    /**
     * 缺失打卡规则和班次规则的提醒消息
     */
    private JSONObject buildNoPunchAndClassRuleReminderMessage(String userCode, String userName, String classNature) {
        String punchClassConfigSuffix = StringUtils.equals(classNature, ClassNatureEnum.FIXED_CLASS.getCode())
                ? PUNCH_FIXED_CLASS_RULE_CONFIG_SUFFIX
                : PUNCH_MULTIPLE_CLASS_RULE_CONFIG_SUFFIX;
        String punchClassConfigUrl = attendanceEnvHost + punchClassConfigSuffix;
        String punchRuleConfigUrl = attendanceEnvHost + PUNCH_RULE_CONFIG_SUFFIX;

        JSONObject templateData = new JSONObject();

        templateData.put("userName", userName);
        templateData.put("userCode", userCode);
        templateData.put("punchClassConfigUrl", punchClassConfigUrl);
        templateData.put("punchConfigUrl", punchRuleConfigUrl);

        return templateData;
    }

    /**
     * 缺失打卡规则的提醒消息
     */
    private JSONObject buildNoPunchRuleReminderMessage(String userCode, String userName) {
        String punchRuleConfigUrl = attendanceEnvHost + PUNCH_RULE_CONFIG_SUFFIX;

        JSONObject templateData = new JSONObject();

        templateData.put("userName", userName);
        templateData.put("userCode", userCode);
        templateData.put("punchConfigUrl", punchRuleConfigUrl);

        return templateData;
    }

    /**
     * 缺失班次规则的提醒消息
     */
    private JSONObject buildClassRuleReminderMessage(String userCode, String userName, String classNature) {
        String punchClassConfigSuffix = StringUtils.equals(classNature, ClassNatureEnum.FIXED_CLASS.getCode())
                ? PUNCH_FIXED_CLASS_RULE_CONFIG_SUFFIX
                : PUNCH_MULTIPLE_CLASS_RULE_CONFIG_SUFFIX;
        String punchClassConfigUrl = attendanceEnvHost + punchClassConfigSuffix;

        JSONObject templateData = new JSONObject();

        templateData.put("userName", userName);
        templateData.put("userCode", userCode);
        templateData.put("punchClassConfigUrl", punchClassConfigUrl);

        return templateData;
    }


}

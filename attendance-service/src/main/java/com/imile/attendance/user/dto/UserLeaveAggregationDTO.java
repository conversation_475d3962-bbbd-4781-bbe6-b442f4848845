package com.imile.attendance.user.dto;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工假期聚合DTO
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveAggregationDTO {

    /**
     * 员工主键
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 帐号
     */
    private String userCode;

    /**
     * 用户对应的假期范围
     */
    List<CompanyLeaveConfigRangDO> userLeaveConfigRangList;

    /**
     * 用户所在假期范围内对应的假期规则
     */
    List<CompanyLeaveConfigDO> userLeaveConfigListForRange;

    /**
     * 用户对应的假期详情
     */
    List<UserLeaveDetailDO> userLeaveDetailList;

    /**
     * 用户假期详情对应的假期规则
     */
    List<CompanyLeaveConfigDO> userLeaveConfigListForDetail;

    /**
     * 用户对应的假期比例阶段
     */
    List<CompanyLeaveItemConfigDO> userLeaveItemConfigList;

    /**
     * 用户对应的假期余额
     */
    List<UserLeaveStageDetailDO> userLeaveStageDetailList;

    /**
     * 用户对应的入职记录
     */
    AttendanceUserEntryRecord userEntryRecord;

    /**
     * 用户对应的假期结转规则
     */
    List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList;

    /**
     * 用户对应的假期结转规则范围
     */
    List<CompanyLeaveConfigCarryOverRangeDO> companyLeaveConfigCarryOverRangeList;
}

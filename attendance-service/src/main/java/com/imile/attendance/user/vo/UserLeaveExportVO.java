package com.imile.attendance.user.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/5/27
 * @Description 员工余额导出VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveExportVO {

    /**
     * 姓名
     */
    private String userName;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 是否有效
     */
    private String isEffect;

    /**
     * 假期有效期
     */
    private String effectTimeStr;

    /**
     * 是否派遣假
     */
    private String isDispatch;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 是否结转
     */
    private String leaveMark;

    /**
     * 使用条件
     */
    private String useCondition;

    /**
     * 假期额度
     */
    private BigDecimal leaveTotalMinutes;

    /**
     * 假期额度(字符串类型)
     */
    private String leaveTotalMinutesStr;

    /**
     * 已使用额度
     */
    private BigDecimal leaveUsedMinutes;

    /**
     * 已使用额度(字符串类型)
     */
    private String leaveUsedMinutesStr;

    /**
     * 假期剩余时间(分钟) 余额
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 假期剩余时间(分钟) 余额(字符串类型)
     */
    private String leaveResidueMinutesStr;

    /**
     * 发薪比例
     */
    private BigDecimal percentSalary;
}

package com.imile.attendance.user;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.archive.service.AttendanceArchiveService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.vacation.AttendanceLeaveRestrictionEnum;
import com.imile.attendance.enums.vacation.CompanyLeaveTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsSalary;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveBalanceDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveRecordImportDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;
import com.imile.attendance.user.dto.UserLeaveAggregationDTO;
import com.imile.attendance.user.dto.UserLeaveBalanceImportDTO;
import com.imile.attendance.user.mapstruct.UserLeaveMapstruct;
import com.imile.attendance.user.vo.UserLeaveExportVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.IpepUtils;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveTypeService;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.attendance.vacation.dto.CompanyLeaveTypeDTO;
import com.imile.common.page.PaginationResult;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户假期余额服务
 *
 * <AUTHOR>
 * @menu 假期
 * @date 2025/5/30
 */
@Service
@Slf4j
public class UserLeaveIpepService {
    @Resource
    UserLeaveService userLeaveService;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private CompanyLeaveTypeService companyLeaveTypeService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private AttendanceArchiveService attendanceArchiveService;
    @Resource
    private AttendanceUserEntryRecordService attendanceUserEntryRecordService;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,4})?$");

    /**
     * 用户假期余额导入
     *
     * @param importList
     * @return
     */
    public List<UserLeaveBalanceImportDTO> leaveBalanceImport(List<UserLeaveBalanceImportDTO> importList) {
        List<UserLeaveBalanceImportDTO> successList = new ArrayList<>();
        List<UserLeaveBalanceImportDTO> failList = new ArrayList<>();
        // 1.query build
        List<String> userCodeList = importList
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getUserCode()))
                .map(item -> item.getUserCode())
                .collect(Collectors.toList());
        List<UserLeaveAggregationDTO> userLeaveAggregationList = userLeaveService.selectUserLeaveAggregation(userCodeList);
        if (CollectionUtils.isEmpty(userLeaveAggregationList)) {
            return Collections.emptyList();
        }
        // 2.import check
        this.leaveBalanceImportDataCheck(importList, userLeaveAggregationList, successList, failList);
        // 3.import handler
        this.leaveBalanceImportHandler(successList, failList);

        return failList;
    }

    /**
     * 假期余额导入校验
     *
     * @param importList
     * @param userLeaveAggregationList
     * @param successList
     * @param failList
     */
    private void leaveBalanceImportDataCheck(List<UserLeaveBalanceImportDTO> importList,
                                             List<UserLeaveAggregationDTO> userLeaveAggregationList,
                                             List<UserLeaveBalanceImportDTO> successList,
                                             List<UserLeaveBalanceImportDTO> failList) {


        for (UserLeaveBalanceImportDTO importDTO : importList) {
            // 基本信息必填校验和参数转换
            if (!this.checkAndBuildBasicRequired(importDTO, failList)) {
                continue;
            }
            // 假期规则业务逻辑校验和参数转换
            if (!this.checkAndBuildImportLeaveConfig(importDTO, userLeaveAggregationList, failList)) {
                continue;
            }
            successList.add(importDTO);
        }
    }

    /**
     * 必填信息校验
     *
     * @param importDTO
     * @return
     */
    private Boolean checkAndBuildBasicRequired(UserLeaveBalanceImportDTO importDTO,
                                               List<UserLeaveBalanceImportDTO> failList) {
        if (StringUtils.isBlank(importDTO.getUserCode())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "员工账号不能为空" : "user code cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getUserName())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "员工姓名不能为空" : "user name cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getIsDispatch())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "是否派遣假不能为空" : "isDispatch cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getCountry())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "假期国家不能为空" : "country cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getLeaveType())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "假期类型不能为空" : "leaveType cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getLeaveName())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "假期名称不能为空" : "leaveName cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getLeaveMark())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "是否结转不能为空" : "leaveMark cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getLeaveBalanceDays())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "假期额度不能为空" : "leaveBalanceDays cannot be empty");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(importDTO.getRemark())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "备注不能为空" : "remark cannot be empty");
            failList.add(importDTO);
            return false;
        }
        String isDispatch = importDTO.getIsDispatch();
        if (!WhetherEnum.descCnList().contains(isDispatch)
                && !WhetherEnum.descEnList().contains(isDispatch.toUpperCase())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "是否派遣假只能填写\"是\"或\"否\""
                    : "isDispatch can only be filled with \"Yes\" or \"no\"");
            failList.add(importDTO);
            return false;
        }
        String leaveMark = importDTO.getLeaveMark();
        if (!WhetherEnum.descCnList().contains(leaveMark)
                && !WhetherEnum.descEnList().contains(leaveMark.toUpperCase())) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "是否结转只能填写\"是\"或\"否\""
                    : "leaveMark can only be filled with \"Yes\" or \"no\"");
            failList.add(importDTO);
            return false;
        }

        // 转换假期类型, 校验假期类型是否存在
        CompanyLeaveTypeQuery queryType = CompanyLeaveTypeQuery.builder().country(importDTO.getCountry()).build();
        List<CompanyLeaveTypeDTO> companyLeaveTypeList = companyLeaveTypeService.queryByCondition(queryType);
        if (CollectionUtils.isEmpty(companyLeaveTypeList)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "国家不存在，或该国家下没有配置假期类型"
                    : "The country does not exist, or there are no leave type in this country");
            failList.add(importDTO);
            return false;
        }
        List<CompanyLeaveTypeDTO> leaveType = companyLeaveTypeList.stream()
                .filter(item -> importDTO.getLeaveType().equals(item.getLeaveTypeEn())
                        || importDTO.getLeaveType().equals(item.getLeaveTypeCn()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveType)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该国家下没有导入的假期类型"
                    : "There are no leave type in this country");
            failList.add(importDTO);
            return false;
        }
        importDTO.setLeaveTypeCode(leaveType.get(0).getLeaveType());

        // 转换余额分钟数
        WhetherEnum isDispatchEnum = WhetherEnum.valueOfDesc(isDispatch);
        if (Objects.nonNull(isDispatchEnum) && !isDispatchEnum.getKey().equals(-1)) {
            importDTO.setIsDispatchNum(isDispatchEnum.getKey());
        }
        WhetherEnum leaveMarkEnum = WhetherEnum.valueOfDesc(leaveMark);
        if (Objects.nonNull(leaveMarkEnum) && !leaveMarkEnum.getKey().equals(-1)) {
            importDTO.setLeaveMarkNum(leaveMarkEnum.getKey());
        }
        String leaveBalanceDays = importDTO.getLeaveBalanceDays();
        BigDecimal leaveBalanceDaysNum = validateNumber(leaveBalanceDays, 999);
        if (BusinessConstant.NEGATIVE_ONE.equals(leaveBalanceDaysNum)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "假期额度填写规范为[0, 999]范围内的数字,最多保留4位小数"
                    : "The standard for filling in the leaveBalanceDays is the number within the range of [0, 999], with a maximum of four decimal places retained");
            failList.add(importDTO);
            return false;
        }

        // 转换分钟数
        importDTO.setLeaveBalanceMinutes(BusinessConstant.DEFAULT_LEGAL_WORKING_MINUTES.multiply(leaveBalanceDaysNum));
        return true;
    }

    /**
     * 导入假期规则条件校验
     *
     * @param importDTO
     * @param userLeaveAggregationList
     * @param failList
     * @return
     */
    private Boolean checkAndBuildImportLeaveConfig(UserLeaveBalanceImportDTO importDTO,
                                                   List<UserLeaveAggregationDTO> userLeaveAggregationList,
                                                   List<UserLeaveBalanceImportDTO> failList) {
        // 获取匹配的人员聚合信息
        List<UserLeaveAggregationDTO> existUserInfoList = userLeaveAggregationList
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getUserCode(), importDTO.getUserCode().trim()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existUserInfoList)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "无法查询到匹配的人员" : "employee not exist");
            failList.add(importDTO);
            return false;
        }
        UserLeaveAggregationDTO userLeaveAggregationDTO = existUserInfoList.get(0);
        importDTO.setUserId(userLeaveAggregationDTO.getId());
        // 获取用户绑定范围内的假期
        List<CompanyLeaveConfigDO> userLeaveConfigListForRange = userLeaveAggregationDTO.getUserLeaveConfigListForRange();
        // 获取用户拥有的假期
        List<CompanyLeaveConfigDO> userLeaveConfigListForDetail = userLeaveAggregationDTO.getUserLeaveConfigListForDetail();
        userLeaveConfigListForRange.addAll(userLeaveConfigListForDetail);
        List<CompanyLeaveConfigDO> userLeaveConfigList = userLeaveConfigListForRange
                .stream()
                .filter(item -> importDTO.getIsDispatchNum().equals(item.getIsDispatch())
                        && importDTO.getCountry().equals(item.getCountry())
                        && importDTO.getLeaveTypeCode().equals(item.getLeaveType())
                        && importDTO.getLeaveName().equals(item.getLeaveName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userLeaveConfigList)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "无法查询到匹配的假期" : "The vacation cannot be found");
            failList.add(importDTO);
            return false;
        }
        // 发薪比例校验
        String percentSalary = importDTO.getPercentSalary();
        BigDecimal percentSalaryNum = validateNumber(percentSalary, 100);
        if (StringUtils.isNotBlank(percentSalary)
                && BusinessConstant.NEGATIVE_ONE.equals(percentSalaryNum)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "发薪比例填写规范为[0, 100]范围内的数字,最多保留4位小数"
                    : "The percentSalary should be filled in as a number within the range of [0, 100], with a maximum of four decimal places retained");
            failList.add(importDTO);
            return false;
        }
        // 封装导入时候匹配到的假期
        CompanyLeaveConfigDO userLeaveConfig = userLeaveConfigList.get(0);
        importDTO.setConfigId(userLeaveConfig.getId());
        importDTO.setUserLeaveConfigForImport(userLeaveConfig);
        // 获取假期结转规则及范围
        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = userLeaveAggregationDTO.getCompanyLeaveConfigCarryOverList();
        List<CompanyLeaveConfigCarryOverRangeDO> companyLeaveConfigCarryOverRangeList = userLeaveAggregationDTO.getCompanyLeaveConfigCarryOverRangeList();
        List<CompanyLeaveConfigCarryOverDO> userLeaveConfigCarryOverList = Optional.ofNullable(companyLeaveConfigCarryOverList)
                .orElse(Lists.newArrayList())
                .stream().filter(item -> userLeaveConfig.getId().equals(item.getLeaveId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userLeaveConfigCarryOverList)) {
            // 封装假期结转规则及范围
            CompanyLeaveConfigCarryOverDO companyLeaveConfigCarryOverDO = userLeaveConfigCarryOverList.get(0);
            importDTO.setUserLeaveConfigCarryOverForImport(companyLeaveConfigCarryOverDO);
            importDTO.setUserLeaveConfigCarryOverRangeForImport(Optional.ofNullable(companyLeaveConfigCarryOverRangeList)
                    .orElse(Lists.newArrayList())
                    .stream().filter(item -> companyLeaveConfigCarryOverDO.getId().equals(item.getCarryOverId()))
                    .collect(Collectors.toList()));
        }
        // 封装入职记录
        importDTO.setUserEntryRecord(userLeaveAggregationDTO.getUserEntryRecord());
        BigDecimal percentSalaryConfig = LeaveConfigIsSalary.UNPAID_LEAVE.getType().equals(userLeaveConfig.getIsSalary())
                ? BigDecimal.ZERO : BusinessConstant.HUNDRED;
        // 封装导入时候对应该假期的发薪比例（数字）
        importDTO.setPercentSalaryNum(percentSalaryNum);
        if (StringUtils.isBlank(percentSalary)) {
            importDTO.setPercentSalaryNum(percentSalaryConfig);
        }
        List<CompanyLeaveItemConfigDO> userLeaveItemConfigList = Optional.ofNullable(userLeaveAggregationDTO.getUserLeaveItemConfigList())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(item -> userLeaveConfigList.get(0).getId().equals(item.getLeaveId()))
                .collect(Collectors.toList());
        // 获取假期规则对应的发薪比例
        if (CollectionUtils.isEmpty(userLeaveItemConfigList)
                && StringUtils.isNotBlank(percentSalary)
                && percentSalaryConfig.compareTo(importDTO.getPercentSalaryNum()) != 0) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该假期规则不存在对应的发薪比例"
                    : "There is no corresponding percent salary for this leave name");
            failList.add(importDTO);
            return false;
        }
        List<BigDecimal> percentSalaryList = userLeaveItemConfigList.stream()
                .map(item -> BusinessConstant.HUNDRED.multiply(item.getPercentSalary()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userLeaveItemConfigList)
                && !percentSalaryList.stream().anyMatch(item -> item.compareTo(importDTO.getPercentSalaryNum()) == 0)) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该假期规则不存在对应的发薪比例"
                    : "There is no corresponding percent salary for this leave name");
            failList.add(importDTO);
            return false;
        }
        if (StringUtils.isBlank(percentSalary) && userLeaveItemConfigList.size() > 1) {
            IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该假期规则存在多个发薪比例，请填写对应的发薪比例"
                    : "There are multiple percent salary for this leave name. Please fill them in");
            failList.add(importDTO);
            return false;
        }
        // 封装导入时候对应该假期的时段
        importDTO.setUserLeaveItemConfigListForImport(userLeaveItemConfigList);
        // 封装导入时候匹配到的用户假期详情
        List<UserLeaveDetailDO> userLeaveDetailForImport = userLeaveAggregationDTO.getUserLeaveDetailList()
                .stream()
                .filter(item -> userLeaveConfig.getId().equals(item.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userLeaveDetailForImport)) {
            // 当前用户不存在此假期，正常新增
            importDTO.setUserLeaveStageDetailListForImport(Lists.newArrayList());
            return true;
        }
        UserLeaveDetailDO userLeaveDetailDO = userLeaveDetailForImport.get(0);
        // 封装导入时候匹配到的用户假期余额
        importDTO.setUserLeaveDetailForImport(userLeaveDetailDO);
        Long leaveDetailId = userLeaveDetailDO.getId();
        List<UserLeaveStageDetailDO> userLeaveStageForImport = userLeaveAggregationDTO.getUserLeaveStageDetailList()
                .stream()
                .filter(item -> leaveDetailId.equals(item.getLeaveId())
                        && importDTO.getLeaveMarkNum().equals(item.getLeaveMark()))
                .sorted(Comparator.comparing(UserLeaveStageDetailDO::getId).reversed())
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(percentSalary)) {
            userLeaveStageForImport = userLeaveStageForImport.stream()
                    .filter(item -> importDTO.getPercentSalaryNum().compareTo(BusinessConstant.HUNDRED.multiply(item.getPercentSalary())) == 0)
                    .collect(Collectors.toList());
        }
        // 设置对应唯一的一条余额
        importDTO.setUserLeaveStageDetailForImport(CollectionUtils.isEmpty(userLeaveStageForImport)
                ? null
                : userLeaveStageForImport.get(0));
        List<UserLeaveStageDetailDO> userLeaveStageListForImport = userLeaveAggregationDTO.getUserLeaveStageDetailList()
                .stream()
                .filter(item -> leaveDetailId.equals(item.getLeaveId()))
                .collect(Collectors.toList());
        // 设置当前用户该假期的所有余额（包含结转和非结转）
        importDTO.setUserLeaveStageDetailListForImport(userLeaveStageListForImport);
        return true;
    }

    /**
     * 假期余额导入处理
     *
     * @param successList
     * @param failList
     */
    private void leaveBalanceImportHandler(List<UserLeaveBalanceImportDTO> successList,
                                           List<UserLeaveBalanceImportDTO> failList) {
        List<UserLeaveDetailDO> addUserLeaveDetailList = new ArrayList<>();
        List<UserLeaveStageDetailDO> addUserLeaveStageDetailList = new ArrayList<>();
        List<UserLeaveRecordImportDTO> addUserLeaveRecordList = new ArrayList<>();
        List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList = new ArrayList<>();

        for (UserLeaveBalanceImportDTO importDTO : successList) {
            // 获取用户当前假期对应的假期余额
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = importDTO.getUserLeaveStageDetailListForImport();
            UserLeaveStageDetailDO userLeaveStageDetail = importDTO.getUserLeaveStageDetailForImport();
            //假期余额有值
            if (Objects.nonNull(userLeaveStageDetail)) {
                // 更新逻辑
                userLeaveStageDetail.setLeaveResidueMinutes(importDTO.getLeaveBalanceMinutes());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                // 新增记录
                addLeaveRecord(importDTO, userLeaveStageDetailList, addUserLeaveRecordList);
                continue;
            }
            // 如果导入是已结转的并且当前用户不存在这个结转假期，那就校验不让导入
            if (WhetherEnum.YES.getKey().equals(importDTO.getLeaveMarkNum())) {
                failList.add(importDTO);
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese()
                        ? "当前用户不存在该结转假期"
                        : "User does not have this carry-over leaveName");
                continue;
            }
            // 新增逻辑
            addLeaveStage(importDTO, addUserLeaveDetailList, addUserLeaveStageDetailList);
            // 新增记录
            addLeaveRecord(importDTO, userLeaveStageDetailList, addUserLeaveRecordList);
        }
        List<UserLeaveRecordDO> addUserLeaveRecordDOList = UserLeaveMapstruct.INSTANCE.toUserLeaveRecordDO(addUserLeaveRecordList);
        //落库
        userLeaveDetailManage.userLeaveBalanceDaysUpdate(addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordDOList, updateUserLeaveStageDetailList, new ArrayList<>());
    }

    private void importUserLeaveBalanceDaysBuild(BigDecimal days, UserLeaveBalanceImportDTO importDTO, CompanyLeaveTypeEnum companyLeaveTypeEnum,
                                                 List<CompanyLeaveConfigDO> allCompanyLeaveConfigDOList, List<CompanyLeaveItemConfigDO> allLeaveItemConfigDOList,
                                                 List<UserLeaveDetailDO> userLeaveDetailDOList, List<UserLeaveStageDetailDO> userLeaveStageDetailDOList,
                                                 List<UserLeaveDetailDO> addUserLeaveDetailList, List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                 List<UserLeaveRecordDO> addUserLeaveRecordList, List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList) {
        List<CompanyLeaveConfigDO> companyLeaveConfigDOList = allCompanyLeaveConfigDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getCountry(), importDTO.getCountry())
                        && StringUtils.equalsIgnoreCase(item.getLeaveType(), companyLeaveTypeEnum.getCode())
                        && !BusinessConstant.Y.equals(item.getIsDispatch()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
            return;
        }
        CompanyLeaveConfigDO userCompanyLeaveConfig = companyLeaveConfigDOList.get(0);
        List<CompanyLeaveItemConfigDO> leaveItemConfigDOList = allLeaveItemConfigDOList.stream()
                .filter(item -> item.getLeaveId().equals(userCompanyLeaveConfig.getId()))
                .sorted(Comparator.comparing(CompanyLeaveItemConfigDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(leaveItemConfigDOList)) {
//            return;
//        }
        //本次用户的总余额分钟
        BigDecimal allBalanceMinutes = days.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
        //看用户数据库有没有这个假，有就更新，没有就新增
        List<UserLeaveDetailDO> existUserLeaveDetailDOList = userLeaveDetailDOList.stream().filter(item -> item.getUserId().equals(importDTO.getUserId()) && StringUtils.equalsIgnoreCase(item.getLeaveType(), companyLeaveTypeEnum.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existUserLeaveDetailDOList)) {
            UserLeaveDetailDO userLeaveDetailDO = new UserLeaveDetailDO();
            userLeaveDetailDO.setId(defaultIdWorker.nextId());
            userLeaveDetailDO.setConfigId(userCompanyLeaveConfig.getId());
            userLeaveDetailDO.setUserId(importDTO.getUserId());
            userLeaveDetailDO.setUserCode(importDTO.getUserCode());
            userLeaveDetailDO.setLeaveName(userCompanyLeaveConfig.getLeaveName());
            userLeaveDetailDO.setLeaveType(companyLeaveTypeEnum.getCode());
            userLeaveDetailDO.setStatus(companyLeaveConfigDOList.get(0).getStatus());
            BaseDOUtil.fillDOInsertByUsrOrSystem(userLeaveDetailDO);
            addUserLeaveDetailList.add(userLeaveDetailDO);
            for (CompanyLeaveItemConfigDO leaveItemConfig : leaveItemConfigDOList) {
                if (allBalanceMinutes.compareTo(BigDecimal.ZERO) < 1) {
                    break;
                }

            }

            UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
            recordDO.setId(defaultIdWorker.nextId());
            recordDO.setConfigId(userCompanyLeaveConfig.getId());
            recordDO.setUserId(importDTO.getUserId());
            recordDO.setUserCode(importDTO.getUserCode());
            recordDO.setDate(new Date());
            recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
            recordDO.setLeaveName(userCompanyLeaveConfig.getLeaveName());
            recordDO.setLeaveType(companyLeaveTypeEnum.getCode());
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
            recordDO.setLeaveMinutes(days.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));
            recordDO.setRemark("2025年中国区年假新规工龄刷新");
            BaseDOUtil.fillDOInsertByUsrOrSystem(recordDO);
            recordDO.setOperationUserCode(RequestInfoHolder.getUserCode());
            recordDO.setOperationUserName(RequestInfoHolder.getUserName());
            addUserLeaveRecordList.add(recordDO);
            return;
        }

        List<UserLeaveStageDetailDO> existUserLeaveStageDetailDOList = userLeaveStageDetailDOList.stream().filter(item -> item.getLeaveId().equals(existUserLeaveDetailDOList.get(0).getId())).sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existUserLeaveStageDetailDOList)) {
            return;
        }
    }

    /**
     * 用户假期余额导出
     *
     * @param query
     * @return
     */
    public PaginationResult<UserLeaveExportVO> leaveBalanceExport(AttendanceArchiveListQuery query) {
        // 根据员工档案列表查询
        PageInfo<UserArchiveDTO> pageInfo = attendanceArchiveService.selectArchiveListPage(query);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 查询员工余额
        List<Long> userIds = pageInfo.getList()
                .stream()
                .map(item -> item.getId())
                .collect(Collectors.toList());
        List<UserLeaveBalanceDTO> userLeaveResidualList = userLeaveDetailManage.selectBatchUserResidual(userIds);
        // VO转换
        List<UserLeaveExportVO> userLeaveExportVOList = this.convertUserLeaveExportVO(userLeaveResidualList, pageInfo.getList());
        return PageUtil.getPageResult(userLeaveExportVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    private List<UserLeaveExportVO> convertUserLeaveExportVO(List<UserLeaveBalanceDTO> userLeaveResidualList,
                                                             List<UserArchiveDTO> userArchiveDTOList) {
        List<CompanyLeaveTypeDTO> companyLeaveTypeList = companyLeaveTypeService.queryByCondition(new CompanyLeaveTypeQuery());
        Map<String, List<CompanyLeaveTypeDTO>> leaveTypeMap = companyLeaveTypeList.stream()
                .collect(Collectors.groupingBy(item -> item.getCountry() + "-" + item.getLeaveType()));
        // 封装返回实体
        List<UserLeaveExportVO> userLeaveExportVOList = Lists.newArrayList();
        Map<Long, List<UserLeaveBalanceDTO>> userResidualMap = userLeaveResidualList
                .stream()
                .collect(Collectors.groupingBy(UserLeaveBalanceDTO::getUserId));
        for (UserArchiveDTO userArchiveDTO : userArchiveDTOList) {
            List<UserLeaveBalanceDTO> userLeaveResidualDTOList = userResidualMap.get(userArchiveDTO.getId());
            if (CollectionUtils.isEmpty(userLeaveResidualDTOList)) {
                // 该员工不存在余额
                userLeaveExportVOList.add(UserLeaveExportVO
                        .builder()
                        .userCode(userArchiveDTO.getUserCode())
                        .userName(userArchiveDTO.getUserName())
                        .build());
                continue;
            }
            for (UserLeaveBalanceDTO item : userLeaveResidualDTOList) {
                String leaveTypeDesc = item.getLeaveType();
                List<CompanyLeaveTypeDTO> leaveType = leaveTypeMap.get(item.getCountry() + "-" + item.getLeaveType());
                if (CollectionUtils.isNotEmpty(leaveType) && RequestInfoHolder.isChinese()) {
                    leaveTypeDesc = leaveType.get(0).getLeaveTypeCn();
                }
                UserLeaveExportVO exportVO = UserLeaveExportVO
                        .builder()
                        .userCode(item.getUserCode())
                        .userName(item.getUserName())
                        .country(item.getCountry())
                        .leaveType(leaveTypeDesc)
                        .leaveName(item.getLeaveName())
                        .leaveTotalMinutes(item.getLeaveTotalMinutes())
                        .leaveUsedMinutes(item.getLeaveUsedMinutes())
                        .leaveResidueMinutes(item.getLeaveResidueMinutes())
                        .percentSalary(Objects.isNull(item.getPercentSalary())
                                ? item.getPercentSalary()
                                : BusinessConstant.HUNDRED.multiply(item.getPercentSalary()))
                        .isEffect(WhetherEnum.valueOfKey(item.getIsInvalid() == 0 ? 1 : 0).getDesc())
                        .isDispatch(WhetherEnum.valueOfKey(item.getIsDispatch()).getDesc())
                        .leaveMark(WhetherEnum.valueOfKey(item.getLeaveMark()).getDesc())
                        // 将分钟数转换为页面显示的天-小时-分钟格式
                        .leaveTotalMinutesStr(CommonUtil.convertMinutesToStr(item.getLeaveTotalMinutes()))
                        .leaveUsedMinutesStr(CommonUtil.convertMinutesToStr(item.getLeaveUsedMinutes()))
                        .leaveResidueMinutesStr(CommonUtil.convertMinutesToStr(item.getLeaveResidueMinutes()))
                        // 转换有效期格式
                        .effectTimeStr(Optional.ofNullable(item.getIssueDate())
                                .map(e -> DateFormatterUtil.dayIdFormatStr(e))
                                .orElse(null)
                                + "~" +
                                Optional.ofNullable(item.getInvalidDate())
                                        .map(e -> DateFormatterUtil.dayIdFormatStr(e))
                                        .orElse(null))
                        .build();
                AttendanceLeaveRestrictionEnum isInvalidEnum = AttendanceLeaveRestrictionEnum.getIsInvalidEnum(item.getUseCondition());
                exportVO.setUseCondition(Objects.isNull(isInvalidEnum) ? null : isInvalidEnum.getValue());
                userLeaveExportVOList.add(exportVO);
            }
        }
        return userLeaveExportVOList;
    }

    private BigDecimal validateNumber(String value, Integer range) {
        if (StringUtils.isBlank(value)) {
            return BusinessConstant.NEGATIVE_ONE;
        }

        // 检查负号
        if (value.contains("-")) {
            return BusinessConstant.NEGATIVE_ONE;
        }

        // 检查格式
        if (!DECIMAL_PATTERN.matcher(value).matches()) {
            return BusinessConstant.NEGATIVE_ONE;
        }

        try {
            BigDecimal number = new BigDecimal(value);

            // 检查范围 [0, 999] / [0, 100]
            if (number.compareTo(BigDecimal.ZERO) < 0) {
                return BusinessConstant.NEGATIVE_ONE;
            }
            if (number.compareTo(new BigDecimal(range)) > 0) {
                return BusinessConstant.NEGATIVE_ONE;
            }

            // 检查小数位数
            int decimalPlaces = number.scale();
            if (decimalPlaces > 4) {
                return BusinessConstant.NEGATIVE_ONE;
            }

            return number;
        } catch (NumberFormatException e) {
            return BusinessConstant.NEGATIVE_ONE;
        }
    }

    private void addLeaveRecord(UserLeaveBalanceImportDTO importDTO,
                                List<UserLeaveStageDetailDO> userLeaveStageDetailList,
                                List<UserLeaveRecordImportDTO> addUserLeaveRecordList) {
        // 记录更新的余额
        BigDecimal leaveBalanceMinutes = importDTO.getLeaveBalanceMinutes();
//        List<UserLeaveRecordImportDTO> userLeaveRecordList = addUserLeaveRecordList
//                .stream()
//                .filter(item -> importDTO.getUserId().equals(item.getUserId())
//                        && importDTO.getConfigId().equals(item.getConfigId()))
//                .collect(Collectors.toList());
//        // 存在一次性导入同一个人相同的假期，可能为不同比例或者不同的结转标识(不同比例、不同结转标识对应一条记录，需要累加余额记录)
//        if (CollectionUtils.isNotEmpty(userLeaveRecordList)) {
//            // 获取不同比例的记录
//            Map<BigDecimal, List<UserLeaveRecordImportDTO>> diffPercentRecordMap = userLeaveRecordList.stream()
//                    .collect(Collectors.groupingBy(UserLeaveRecordImportDTO::getPercentSalary));
//            // 遍历当前用户对应的假期余额（包含结转非结转）
//            for (UserLeaveStageDetailDO userLeaveStageDetailDO : userLeaveStageDetailList) {
//                // 是当前导入的 跳过
//                if (userLeaveStageDetailDO.getPercentSalary().compareTo(importDTO.getPercentSalaryNum()) == 0
//                        && userLeaveStageDetailDO.getLeaveMark().equals(importDTO.getLeaveMarkNum())) {
//                    continue;
//                }
//                // 不是当前导入的 需要判断是否存在导入的不同比例记录
//                List<UserLeaveRecordImportDTO> diffPercentRecordList = diffPercentRecordMap.get(userLeaveStageDetailDO.getPercentSalary());
//                if (CollectionUtils.isEmpty(diffPercentRecordList)) {
//                    // 不存在，则累加用户当前假期余额
//                    leaveBalanceMinutes = leaveBalanceMinutes.add(userLeaveStageDetailDO.getLeaveResidueMinutes());
//                }
//                // 存在，则需要判断结转标识是否跟当前假期一致，不一致累加假期余额，一致累加记录余额
//                List<BigDecimal> sameRecordMinutes = diffPercentRecordList.stream()
//                        .filter(item -> userLeaveStageDetailDO.getLeaveMark().equals(item.getLeaveMark()))
//                        .map(UserLeaveRecordImportDTO::getLeaveMinutes)
//                        .collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(sameRecordMinutes)) {
//                    leaveBalanceMinutes = leaveBalanceMinutes.add(userLeaveStageDetailDO.getLeaveResidueMinutes());
//                    continue;
//                }
//                for (BigDecimal sameRecordMinute : sameRecordMinutes) {
//                    leaveBalanceMinutes = leaveBalanceMinutes.add(sameRecordMinute);
//                }
//            }
//        }
        UserLeaveRecordImportDTO recordDTO = UserLeaveRecordImportDTO
                .builder()
                .id(defaultIdWorker.nextId())
                .userId(importDTO.getUserId())
                .userCode(importDTO.getUserCode())
                .date(new Date())
                .dayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)))
                .leaveType(importDTO.getLeaveTypeCode())
                .leaveName(importDTO.getLeaveName())
                .configId(importDTO.getConfigId())
                .type(LeaveTypeEnum.IMPORT.getCode())
                .leaveMinutes(leaveBalanceMinutes)
                .leaveMark(importDTO.getLeaveMarkNum())
                .remark(importDTO.getRemark())
                .percentSalary(importDTO.getPercentSalaryNum())
                .build();
        addUserLeaveRecordList.add(recordDTO);
    }

    private void addLeaveStage(UserLeaveBalanceImportDTO importDTO,
                               List<UserLeaveDetailDO> addUserLeaveDetailList,
                               List<UserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        CompanyLeaveConfigDO userLeaveConfigForImport = importDTO.getUserLeaveConfigForImport();
        UserLeaveDetailDO userLeaveDetailForImport = importDTO.getUserLeaveDetailForImport();
        if (Objects.isNull(userLeaveDetailForImport)) {
            userLeaveDetailForImport = new UserLeaveDetailDO();
            userLeaveDetailForImport.setId(defaultIdWorker.nextId());
            userLeaveDetailForImport.setConfigId(userLeaveConfigForImport.getId());
            userLeaveDetailForImport.setUserId(importDTO.getUserId());
            userLeaveDetailForImport.setUserCode(importDTO.getUserCode());
            userLeaveDetailForImport.setLeaveName(importDTO.getLeaveName());
            userLeaveDetailForImport.setLeaveType(importDTO.getLeaveTypeCode());
            userLeaveDetailForImport.setStatus(userLeaveConfigForImport.getStatus());
            BaseDOUtil.fillDOInsertByUsrOrSystem(userLeaveDetailForImport);
            addUserLeaveDetailList.add(userLeaveDetailForImport);
        }
        UserLeaveStageDetailDO detailDO = new UserLeaveStageDetailDO();
        detailDO.setLeaveId(userLeaveDetailForImport.getId());
        detailDO.setLeaveResidueMinutes(importDTO.getLeaveBalanceMinutes());
        detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        detailDO.setStatus(userLeaveConfigForImport.getStatus());
        detailDO.setLeaveMark(importDTO.getLeaveMarkNum());
        detailDO.setIsInvalid(WhetherEnum.NO.getKey());
        detailDO.setIssueDate(DateUtils.date2Str(new Date(), DateFormatterUtil.FORMAT_YYYYMMDD));
        detailDO.setInvalidDate(companyLeaveConfigCarryOverService.getUserInvalidDate(new Date(),
                importDTO.getUserEntryRecord(), importDTO.getUserLeaveConfigCarryOverForImport(),
                importDTO.getUserLeaveConfigCarryOverRangeForImport()));
        BaseDOUtil.fillDOInsert(detailDO);
        BaseDOUtil.fillDOInsertByUsrOrSystem(detailDO);
        // 设置比例阶段
        Integer stage = BusinessConstant.ONE;
        BigDecimal percentSalary = LeaveConfigIsSalary.UNPAID_LEAVE.getType().equals(userLeaveConfigForImport.getIsSalary())
                ? BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
                : BigDecimal.ONE.setScale(2, RoundingMode.HALF_UP);
        List<CompanyLeaveItemConfigDO> userLeaveItemConfigListForImport = importDTO.getUserLeaveItemConfigListForImport();
        if (StringUtils.isNotBlank(importDTO.getPercentSalary())) {
            List<CompanyLeaveItemConfigDO> percentItem = userLeaveItemConfigListForImport.stream()
                    .filter(item -> BusinessConstant.HUNDRED.multiply(item.getPercentSalary()).compareTo(importDTO.getPercentSalaryNum()) == 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(percentItem)) {
                stage = percentItem.get(0).getStage();
                percentSalary = percentItem.get(0).getPercentSalary();
            }
        }
        detailDO.setStage(stage);
        detailDO.setPercentSalary(percentSalary);
        addUserLeaveStageDetailList.add(detailDO);
    }

}

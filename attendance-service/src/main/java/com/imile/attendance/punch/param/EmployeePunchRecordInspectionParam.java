package com.imile.attendance.punch.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/5/10
 * @Description 用户打卡记录巡检处理参数
 */
@Data
public class EmployeePunchRecordInspectionParam {

    /**
     * 常驻国列表
     */
    private List<String> countryList;
    /**
     * 巡检日期:格式比如2024-01-01 00:00:00
     */
    private String inspectDate;

    /**
     * gps or wifi configIdList
     */
    private List<Long> gpsOrWifiConfigIdList;

    /**
     * 过滤指定用户
     */
    private List<String> userCodeList;

    /**
     * 分页，每页多少用户
     */
    private Integer pageSize = 500;

    /**
     * gps间距分钟数:默认1分钟
     */
    private Long gpsIntervalMinutes = 1L;
    /**
     * wifi间距分钟数:默认60分钟
     */
    private Long wifiIntervalMinutes = 60L;

    /**
     * 是否落库
     */
    private Boolean isSaveToDb = true;

}
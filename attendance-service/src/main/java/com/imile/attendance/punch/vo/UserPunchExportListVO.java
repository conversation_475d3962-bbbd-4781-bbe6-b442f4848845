package com.imile.attendance.punch.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
@Data
public class UserPunchExportListVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 日期
     */
    private String date;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 中控人数
     */
    private Integer zkAttendanceNum;

    /**
     * 非司机的活跃人数
     */
    private Integer activeNum;

    /**
     * 注册率
     */
    private String activeRate;

    /**
     * 录入系统的人数
     */
    private Integer registrationNum;

    /**
     * 当天实际应考勤人数
     */
    private Integer actualAttendanceNum;

    /**
     * 脸部/掌纹注册率
     */
    private String registrationRate;

    /**
     * 当天实际打卡人数
     */
    private Integer punchNum;

    /**
     * 打卡率
     */
    private String punchRate;

}

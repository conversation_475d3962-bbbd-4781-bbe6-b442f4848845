package com.imile.attendance.punch;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.punch.PunchCardTypeSearchEnum;
import com.imile.attendance.enums.punch.PunchTypeEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.zkteco.dao.AttendanceCompletionRateDao;
import com.imile.attendance.infrastructure.repository.zkteco.model.AttendanceCompletionRateDO;
import com.imile.attendance.punch.dto.PunchCardRecordResultDTO;
import com.imile.attendance.punch.dto.UserPunchListExportDTO;
import com.imile.attendance.punch.query.PunchCardRecordQuery;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考勤打卡逻辑处理
 *
 * <AUTHOR>
 * @menu 打卡记录
 * @date 2025/5/10
 */
@Slf4j
@Service
public class EmployeePunchRecordService {
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private AttendanceCompletionRateDao completionRateDao;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private AttendanceDeptService deptService;

    /**
     * 用户打卡记录查询（拷贝HRMS 待确认是否有用）
     * @param query
     * @return
     */
    public PaginationResult<PunchCardRecordResultDTO> listPunchCardRecord(PunchCardRecordQuery query) {
        Page<PunchCardRecordResultDTO> page = PageHelper.startPage(query.getCurrentPage(),
                query.getShowCount(), query.getCount());

        List<EmployeePunchRecordDO> recordDOList = queryRecordDOList(query);
        List<PunchCardRecordResultDTO> resultDTOList = new ArrayList<>();
        //把考勤打卡数据按天分组
        Map<String, List<EmployeePunchRecordDO>> recordMap = recordDOList.stream().collect(Collectors.groupingBy(o -> o.getDayId()));
        for (Map.Entry<String, List<EmployeePunchRecordDO>> entry : recordMap.entrySet()) {
            //每天的考勤打卡数据根据人分组
            Map<String, List<EmployeePunchRecordDO>> userRecordMap = entry.getValue().stream().collect(Collectors.groupingBy(o -> o.getUserCode()));
            for (Map.Entry<String, List<EmployeePunchRecordDO>> item : userRecordMap.entrySet()) {
                resultDTOList.add(punchCardRecordResultHandle(item.getValue()));
            }
        }
        return PageUtil.get(resultDTOList, page, query);
    }

    /**
     * 打卡记录列表
     * @param query
     * @return
     */
    public PaginationResult<PunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query) {
        log.info("listRecord query:{}", query);
        // 拷贝HRMS原有权限逻辑，看后续是否保留
        String country = query.getCountry();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(country)) {
            paramCountryList.add(country);
        }
        Boolean isChooseDept = Boolean.FALSE;
        if (CollUtil.isNotEmpty(query.getDeptIdList())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }
        PermissionCountryDeptVO permissionDept = userResourceService.getPermissionCountryDeptVO(query.getDeptIdList()
                , Lists.newArrayList(paramCountryList));
        Boolean hasOrDeptAndCountryPermission = permissionDept.getHasOrDeptAndCountryPermission();
        log.info("listRecord query:{}", permissionDept);

        if (CollectionUtils.isNotEmpty(query.getDeptIdList())
                && permissionDept.getHasDeptPermission().equals(Boolean.FALSE)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        if (hasOrDeptAndCountryPermission.equals(Boolean.FALSE)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        query.setDeptIdList(permissionDept.getDeptIdList());
        query.setAuthLocationCountryList(permissionDept.getCountryList());

        // 非系统管理员
        query.setHasDeptPermission(permissionDept.getHasDeptPermission());
        query.setHasCountryPermission(permissionDept.getHasCountryPermission());
        query.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        query.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        query.setIsChooseDept(isChooseDept);
        log.info("listRecord permissionDept:{}", permissionDept);

        //打卡方式条件转换
        if (StringUtils.isNotBlank(query.getPunchCardType())) {
            PunchCardTypeSearchEnum cardTypeSearchEnum = PunchCardTypeSearchEnum.getInstance(query.getPunchCardType());
            switch (cardTypeSearchEnum) {
                case FACE:
                    query.setPunchCardType(null);
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getFaceList());
                    break;
                case PALM:
                    query.setPunchCardType(null);
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getPalmList());
                    break;
                case FINGER:
                    query.setPunchCardType(null);
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getFingerList());
                    break;
                case PASSWORD:
                    query.setPunchCardType(null);
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getPasswordList());
                    break;
                default:
                    break;
            }
        }
        log.info("listRecord query2:{}", query);
        Page<PunchCardRecordDTO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<PunchCardRecordDTO> pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.listRecord(query));

        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> deptIds = pageInfo.getList().stream().map(o -> o.getDeptId()).distinct().collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIds);
        Map<Long, AttendanceDept> deptMap = deptList
                .stream()
                .collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        for (PunchCardRecordDTO recordDTO : pageInfo.getList()) {
            AttendanceDept entDept = deptMap.get(recordDTO.getDeptId());
            if (entDept != null) {
                recordDTO.setOrganizationCode("");
                recordDTO.setDeptName(RequestInfoHolder.isChinese() ? entDept.getDeptNameCn() : entDept.getDeptNameEn());
            }
        }
        return PageUtil.getPageResult(pageInfo.getList(), query, (int) pageInfo.getTotal(), pageInfo.getPages());

    }

    /**
     * 处理员工考勤打卡结果数据
     *
     * @param recordDOList
     * @return
     */
    private PunchCardRecordResultDTO punchCardRecordResultHandle(List<EmployeePunchRecordDO> recordDOList) {
        PunchCardRecordResultDTO resultDTO = new PunchCardRecordResultDTO();
        if (recordDOList.size() == 1) {
            EmployeePunchRecordDO recordDO = recordDOList.get(0);
            buildPunchCardRecord(resultDTO, recordDO);
            return resultDTO;
        }
        List<EmployeePunchRecordDO> sortList = recordDOList
                .stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime))
                .collect(Collectors.toList());
        buildPunchCardRecord(resultDTO, sortList.get(0));
        buildPunchCardRecord(resultDTO, sortList.get(sortList.size() - 1));
        return resultDTO;
    }

    /**
     * 构建PunchCardRecordResultDTO数据
     *
     * @param resultDTO
     * @param recordDO
     */
    private void buildPunchCardRecord(PunchCardRecordResultDTO resultDTO, EmployeePunchRecordDO recordDO) {
        if (StringUtils.isEmpty(resultDTO.getDayId())) {
            resultDTO.setDayId(recordDO.getDayId());
        }
        if (StringUtils.isEmpty(resultDTO.getCountry())) {
            resultDTO.setCountry(recordDO.getCountry());
        }
        if (StringUtils.isEmpty(resultDTO.getStationCode())) {
            resultDTO.setStationCode(recordDO.getOcCode());
        }
        if (StringUtils.isEmpty(resultDTO.getStationName())) {
            resultDTO.setStationName(recordDO.getOcName());
        }
        if (StringUtils.isEmpty(resultDTO.getStationPoint())) {
            resultDTO.setStationPoint(recordDO.getOcLatitude() + "," + recordDO.getOcLongitude());
        }
        if (StringUtils.isEmpty(resultDTO.getDriverCode())) {
            resultDTO.setDriverCode(recordDO.getUserCode());
        }
        if (StringUtils.isEmpty(resultDTO.getDriverName())) {
            resultDTO.setDriverName(recordDO.getCreateUserName());
        }
        if (StringUtils.isEmpty(resultDTO.getEmployeeType())) {
            resultDTO.setEmployeeType(recordDO.getEmployeeType());
        }
        if (StringUtils.equalsIgnoreCase(PunchTypeEnum.ON_DUTY.getCode(), recordDO.getPunchType())) {
            resultDTO.setOnDutyTime(recordDO.getPunchTime());
            resultDTO.setOnDutyPoint(recordDO.getLatitude() + "," + recordDO.getLongitude());
            resultDTO.setOnDutyDistance(recordDO.getDistance());
        }
        if (StringUtils.equalsIgnoreCase(PunchTypeEnum.OUT_DUTY.getCode(), recordDO.getPunchType())) {
            resultDTO.setOffDutyTime(recordDO.getPunchTime());
            resultDTO.setOffDutyPoint(recordDO.getLatitude() + "," + recordDO.getLongitude());
            resultDTO.setOffDutyDistance(recordDO.getDistance());
        }
    }
    
    /**
     * 打卡记录导出（拷贝HRMS 待确认是否有用）
     * @param query
     * @return
     */
    public PaginationResult<UserPunchListExportDTO> export(UserPunchCardRecordQuery query) {
        buildExportParam(query);
        Page<UserPunchListExportDTO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);

        // TODO: 2023/7/19 组织架构  前端传部门idList并权限过滤
        PageInfo<AttendanceCompletionRateDO> pageInfo = page.doSelectPageInfo(() ->
                completionRateDao.selectListByDayIds(getBetweenDateDayIds(query.getStartDate(), query.getEndDate())));
        List<AttendanceCompletionRateDO> completionRateDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(completionRateDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<UserPunchListExportDTO> punchListExportDTOS = new ArrayList<>();
        for (AttendanceCompletionRateDO rateDO : completionRateDOList) {
            UserPunchListExportDTO userPunchListExportDTO = BeanUtils.convert(rateDO, UserPunchListExportDTO.class);

            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(rateDO.getEmployeeType());
            userPunchListExportDTO.setEmployeeType(employmentTypeEnum == null ? rateDO.getEmployeeType() : RequestInfoHolder.isChinese() ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());

            userPunchListExportDTO.setActiveRate(rateDO.getActiveRate().compareTo(BigDecimal.valueOf(0)) == 0 ? "0%" : rateDO.getActiveRate().compareTo(BigDecimal.valueOf(1)) >= 0 ? "100%" : rateDO.getActiveRate().multiply(BigDecimal.valueOf(BusinessConstant.ONE_HUNDRED_NUM)).setScale(2, BigDecimal.ROUND_HALF_UP) + BusinessConstant.PERCENT);
            userPunchListExportDTO.setRegistrationRate(rateDO.getRegistrationRate().compareTo(BigDecimal.valueOf(0)) == 0 ? "0%" : rateDO.getRegistrationRate().compareTo(BigDecimal.valueOf(1)) >= 0 ? "100%" : rateDO.getRegistrationRate().multiply(BigDecimal.valueOf(BusinessConstant.ONE_HUNDRED_NUM)).setScale(2, BigDecimal.ROUND_HALF_UP) + BusinessConstant.PERCENT);
            userPunchListExportDTO.setPunchRate(rateDO.getPunchRate().compareTo(BigDecimal.valueOf(0)) == 0 ? "0%" : rateDO.getPunchRate().compareTo(BigDecimal.valueOf(1)) >= 0 ? "100%" : rateDO.getPunchRate().multiply(BigDecimal.valueOf(BusinessConstant.ONE_HUNDRED_NUM)).setScale(2, BigDecimal.ROUND_HALF_UP) + BusinessConstant.PERCENT);
            punchListExportDTOS.add(userPunchListExportDTO);
        }
        List<AttendanceDept> deptDOList = deptService.listByDeptIds(punchListExportDTOS
                .stream()
                .map(UserPunchListExportDTO::getDeptId)
                .collect(Collectors.toList()));
        Map<Long, AttendanceDept> entDeptMap = deptDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
        for (UserPunchListExportDTO item : punchListExportDTOS) {
            item.setDate(DateFormatterUtil.dayIdFormat(item.getDayId()));
            if (entDeptMap.containsKey(item.getDeptId())) {
                AttendanceDept deptDO = entDeptMap.get(item.getDeptId());
                item.setOrgCode("");
                item.setDeptName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
                item.setCountry(deptDO.getBizCountry());
            }
        }
        //添加国家过滤
        if (StringUtils.isNotBlank(query.getCountry())) {
            punchListExportDTOS = punchListExportDTOS.stream().filter(item -> {
                String[] bizCountry = item.getCountry().split(",");
                List<String> countryList = Arrays.asList(bizCountry);
                return countryList.contains(query.getCountry());
            }).collect(Collectors.toList());
        }

        return PageUtil.getPageResult(punchListExportDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 获取两个日期间的dayId
     */
    List<Long> getBetweenDateDayIds(Date startDate, Date endDate) {
        List<Long> dayIds = new ArrayList<>();
        Long startDayId = Long.parseLong(DateUtil.format(startDate, DatePattern.PURE_DATE_PATTERN));
        dayIds.add(startDayId);
        Long endDayId = Long.parseLong(DateUtil.format(endDate, DatePattern.PURE_DATE_PATTERN));
        if (!startDayId.equals(endDayId)) {
            dayIds.addAll(getBetweenDateDayIds(DateHelper.pushDate(startDate, BusinessConstant.ONE), endDate));
        }
        return dayIds;
    }

    /**
     * 转换导入参数
     * @param query
     */
    private void buildExportParam(UserPunchCardRecordQuery query) {
        if (StringUtils.isNotBlank(query.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(query.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            query.setDeptIdList(deptIdList);
        }
        if (StringUtils.isNotBlank(query.getUserCodeString())) {
            List<String> userCodeList = Arrays.asList(query.getUserCodeString().split(","));
            query.setUserCodes(userCodeList);
        }
    }
    
    /**
     * 打卡记录导出
     * @param query
     * @return
     */
    public PaginationResult<PunchCardRecordDTO> punchRecordListExport(UserPunchCardRecordQuery query) {
        buildExportParam(query);
        if (DateHelper.getDateDiff(query.getStartDate(),
                query.getEndDate()) > BusinessConstant.MONTH_DAY) {
            throw BusinessException.get(ErrorCodeEnum.EXPORT_TIME_RANGE_CANNOT_EXCEED_ONE_MONTH.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.EXPORT_TIME_RANGE_CANNOT_EXCEED_ONE_MONTH.getDesc()));
        }
        if (StringUtils.isNotBlank(query.getDeptIdString())) {
            List<Long> deptIdList = Arrays.asList(query.getDeptIdString().split(","))
                    .stream().map(item -> Long.valueOf(item))
                    .collect(Collectors.toList());
            query.setDeptIdList(deptIdList);
        }
        return listRecord(query);
    }

    // 原有查询方法
    /**
     * 查询考勤打卡数据
     * @param query
     * @return
     */
    public List<EmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery query) {
        return employeePunchRecordDao.listRecords(query);
    }

    /**
     * 查询考勤打卡数据
     * @param query
     * @return
     */
    private List<EmployeePunchRecordDO> queryRecordDOList(PunchCardRecordQuery query) {
        //查询考勤打卡数据
        EmployeePunchCardRecordQuery queryDTO = EmployeePunchCardRecordQuery.builder()
                .country(query.getCountry())
                .startTime(query.getStartDate())
                .endTime(query.getEndDate())
                .build();
        List<EmployeePunchRecordDO> recordDOList = employeePunchRecordDao.listRecords(queryDTO);
        return recordDOList;
    }

    /**
     * 获取用户对应考勤日下的所有打卡记录
     * @param userCode
     * @param dayIds
     * @return
     */
    public List<EmployeePunchRecordDO> getUserPunchRecords(String userCode, List<Long> dayIds) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(dayIds)) {
            return Lists.newArrayList();
        }
        //获取打卡规则的起始结束时间的打卡数据
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setDayIds(dayIds.stream().map(e -> String.valueOf(e)).collect(Collectors.toList()));
        punchCardRecordQuery.setUserCode(userCode);
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordDao.listRecords(punchCardRecordQuery).stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPunchRecordList)) {
            return Lists.newArrayList();
        }
        return allPunchRecordList;
    }

}

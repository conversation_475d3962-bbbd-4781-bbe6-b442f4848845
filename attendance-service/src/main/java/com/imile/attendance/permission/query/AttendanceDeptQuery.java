package com.imile.attendance.permission.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AttendanceDeptQuery {

    /**
     * 上级部门id
     */
    private List<Long> parentIdList;

    /**
     * 启用状态
     */
    private String status;

    /**
     * 部门状态
     */
    private List<String> statusList;

    /**
     * 是否需要作业部门标记
     */
    private Integer isDriver;

    /**
     * 部门idList
     */
    private List<Long> deptIdList;

    /**
     * 是否过滤国家级网点 1:过滤 0/nul:不过滤
     */
    private Integer filterCenterStation;

    private List<Integer> deptOrgTypeList;

    /**
     * 国家列表
     */
    private List<String> bizCountryList;

    /**
     * 1:目标看板,2团队绩效
     */
    private Integer type;

    /**
     * 是否包含下级部门( 1:是 0/null:不是)
     */
    private Integer includeSubOrg;

    /**
     * 是否无需做权限过滤( 1:是 0/null:不是)
     */
    private Integer isNoPermission;

    private String userCode;

    /**
     * 要排除的业务覆盖国
     */
    private List<String> excludeBizCountryList;

    /**
     * 地理国
     */
    private String country;

    /**
     * 业务领域
     */
    private String bizArea;

    /**
     * 生效时间
     */
    private Date activeStartTime;
    private Date activeEndTime;
}

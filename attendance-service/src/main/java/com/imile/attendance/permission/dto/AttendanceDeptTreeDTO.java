package com.imile.attendance.permission.dto;

import com.imile.attendance.util.BaseTree;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class AttendanceDeptTreeDTO extends BaseTree<Long, AttendanceDeptTreeDTO> {

    private String ocCenterCode;

    /**
     * 是否作业部门
     */
    private Integer isOperationDept;
    /**
     * 是否展开
     */
    private boolean expand;
    /**
     * 状态
     */
    private String status;

    private Long ocId;

    private String ocCode;

    private String ocType;

    private String country;

    private String deptName;

    private String deptNameCn;

    private String deptNameEn;

    private String deptCode;

    private Integer isBizModelRelation;

    private Integer level;

    private String deptPath;

    private String type;

    private Long bizModelId;

    /**
     * 业务领域描述列表
     */
    private List<String> bizAreaNameList;

    /**
     * 业务领域描述列表
     */
    private List<String> bizAreaList;

    /**
     * 负责人名称
     */
    private String leaderName;

    private Boolean isAuth;

    private List<AttendanceDeptAttributeDTO> attributeList;

    /**
     * 是否有权限（1:是 0:否）
     */
    private Integer hasPermission;

    /**
     * 缺失信息数量
     */
    private Integer missingDataNum;

    /**
     * 组织类型
     */
    private String deptOrgType;


    /**
     * 业务国
     */
    private List<String> bizCountryList;
}

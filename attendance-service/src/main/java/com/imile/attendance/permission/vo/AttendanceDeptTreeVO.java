package com.imile.attendance.permission.vo;


import lombok.Data;

import java.util.List;


/**
 * 部门树节点类
 *
 * <AUTHOR>
 */
@Data
public class AttendanceDeptTreeVO {
    /**
     * 节点ID
     */
    private Long id;
    /**
     * 标题
     */
    private String nodeName;

    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;

    /**
     * 父节点ID
     */
    private Long parentId;
    /**
     * 是否子节点
     */
    private boolean hasChild;
    /**
     * 树结构中访问路径
     */
    private List<Long> path;
    /**
     * 子节点
     */
    private List<AttendanceDeptTreeVO> children;

    private String ocCenterCode;

    /**
     * 是否作业部门
     */

    private Integer isOperationDept;
    /**
     * 是否展开
     */
    private boolean expand;
    /**
     * 状态
     */
    private String status;
    /**
     * 网点id（作业部门）
     */
    private Long ocId;
    /**
     * 网点编码
     */
    private String ocCode;
    /**
     * 国家
     */
    private String country;

    private Integer isBizModelRelation;

    private Long bizModelId;

    /**
     * 业务领域列表
     */
    private List<String> bizAreaNameList;

    /**
     * 业务领域列表
     */
    private List<String> bizAreaList;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 是否有权限（1:是 2:否）
     */
    private Integer hasPermission;

    /**
     * 缺失信息数量
     */
    private Integer missingDataNum;

    /**
     * 组织类型
     */
    private Integer deptOrgType;

    /**
     * 业务国
     */
    private List<String> bizCountryList;

    /**
     * 部门名称，已做中英文区分
     */
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    private String type;

    /**
     * 网点类型
     */
    private String ocType;
}

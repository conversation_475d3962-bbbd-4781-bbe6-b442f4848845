package com.imile.attendance.permission.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeptQuery {
    /**
     * 是否需要作业部门标记
     */
    private Integer isDriver;

    /**
     * 国家列表（前端传参调用）
     */
    private List<String> bizCountryList;

    /**
     * 业务国家（内部使用）
     */
    private String bizCountry;

    /**
     * 部门idList
     */
    private List<Long> deptIdList;

    private Long userId;

    /**
     * 是否过滤国家级网点 1:过滤 0/nul:不过滤
     */
    private Integer filterCenterStation;

    /**
     * 地理国家（兼容旧逻辑）
     */
    private String country;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 是否需要做权限过滤 1是 0/null否
     */
    private Integer isNeedPermission;

    /**
     * 父部门
     */
    private List<Long> parentIdList;

    /**
     * 部门状态
     */
    private List<String> statusList;
}

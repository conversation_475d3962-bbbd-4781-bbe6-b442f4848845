package com.imile.attendance.driver.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailMonthParam
 * {@code @since:} 2024-01-24 20:45
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "司机每月考勤入参")
public class DriverAttendanceDetailMonthParam extends ResourceQuery {

    /**
     * 国家
     */
    @ApiModelProperty(value="国家")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * startDayId 示例：20240124
     * 司机月报查询条件
     */
    @ApiModelProperty(value="startDayId 示例：20240124")
    private Long monthStartDayId;

    /**
     * endDayId 示例：20240124
     * 司机月报查询条件
     */
    @ApiModelProperty(value="endDayId 示例：20240124")
    private Long monthEndDayId;

    /**
     * startDate 示例：2024-01-24
     * 司机月报查询条件
     */
    @ApiModelProperty(value="startDate 示例：2024-01-24")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date monthStartDate;

    /**
     * endDate 示例：2024-01-24
     * 司机月报查询条件
     */
    @ApiModelProperty(value="endDate 示例：2024-01-24")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date monthEndDate;

    /**
     * startDate 示例：2024-01-24 00:00:00 用于mybaits 查询sql判断
     * 司机月报查询条件
     */
    @ApiModelProperty(value="startDate 示例：2024-01-24 00:00:00")
    private String monthStartDateString;

    /**
     * endDate 示例：2024-01-24 23:59:59 用于mybaits 查询sql判断
     * 司机月报查询条件
     */
    @ApiModelProperty(value="endDate 示例：2024-01-24 23:59:59")
    private String monthEndDateString;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    @ApiModelProperty(value="出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...")
    private List<Integer> attendanceTypeList;

    /**
     * 用工类型
     */
    @ApiModelProperty(value="用工类型")
    private List<String> employeeTypeList;

    /**
     * 账号状态
     */
    @ApiModelProperty(value="账号状态")
    private String status;

    /**
     * 工作状态
     */
    @ApiModelProperty(value="工作状态")
    private String workStatus;

    /**
     * 工作状态集合
     */
    @ApiModelProperty(value="工作状态")
    private List<String> workStatusList;

    /**
     * 供应商code
     */
    @ApiModelProperty(value="供应商code")
    private String vendorCode;

    /**
     * 账号/姓名
     */
    @ApiModelProperty(value="账号/姓名")
    private String userCodeOrName;

    /**
     * 部门id
     */
    @ApiModelProperty(value="部门id")
    private List<Long> deptIdList;

    /**
     * 汇报上级
     */
    @ApiModelProperty(value="汇报上级")
    private Long leaderId;

    /**
     * 用户code集合（薪酬增加）
     */
    @ApiModelProperty(value="用户code集合（薪酬增加）")
    private List<String> userCodeList;
}

package com.imile.attendance.archive.service;

import com.github.pagehelper.PageInfo;
import com.imile.attendance.archive.command.AttendanceArchiveUpdateCommand;
import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.archive.query.RuleModifyRecordQuery;
import com.imile.attendance.archive.vo.AttendanceArchiveDetailVO;
import com.imile.attendance.archive.vo.AttendanceArchiveUpdateConfirmVO;
import com.imile.attendance.archive.vo.AttendanceArchiveVO;
import com.imile.attendance.archive.vo.ModifyRecordModuleVO;
import com.imile.attendance.archive.vo.RuleModifyRecordVO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.common.page.PaginationResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
public interface AttendanceArchiveService {

    /**
     * 员工考勤档案列表(DTO内部使用)
     */
    PageInfo<UserArchiveDTO> selectArchiveListPage(AttendanceArchiveListQuery query);

    /**
     * 员工考勤档案列表
     */
    PaginationResult<AttendanceArchiveVO> page(AttendanceArchiveListQuery query);


    /**
     * 员工考勤档案详情
     */
    AttendanceArchiveDetailVO detail(String userCode);

    /**
     * 考勤档案编辑前置处理
     */
    AttendanceArchiveUpdateConfirmVO updatePreProcessor(AttendanceArchiveUpdateCommand command);

    /**
     * 考勤档案编辑
     */
    void update(AttendanceArchiveUpdateCommand command);

    /**
     * 变更记录模块下拉
     */
    List<ModifyRecordModuleVO> moduleSelect();

    /**
     * 员工变更记录列表
     */
    List<RuleModifyRecordVO> ruleModifyList(RuleModifyRecordQuery query);
}

package com.imile.attendance.archive.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AttendanceArchiveVO implements Serializable {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文姓名
     */
    private String userNameEn;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态描述
     */
    private String workStatusDesc;

    /**
     * 状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型名称
     */
    private String employeeTypeDesc;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否司机描述
     */
    private String isDriverDesc;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 是否派遣描述
     */
    private String isGlobalRelocationDesc;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 班次性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CLASS_NATURE, ref = "classNatureDesc")
    private String classNature;

    /**
     * 班次性质描述
     */
    private String classNatureDesc;

    /**
     * 班次名称
     */
    private String classNameStr;

    /**
     * 日历ID
     */
    private Long calendarId;

    /**
     * 日历名称
     */
    private String calendarName;

    /**
     * 打卡规则ID
     */
    private Long punchConfigId;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 补卡规则ID
     */
    private Long reissueCardConfigId;

    /**
     * 补卡规则名称
     */
    private String reissueCardConfigName;

    /**
     * 加班规则ID
     */
    private Long overTimeConfigId;

    /**
     * 加班规则名称
     */
    private String overTimeConfigName;

    /**
     * 打卡方式
     */
    private String punchCardTypeStr;

    /**
     * 入职时间
     */
    private String entryTimeStr;

    /**
     * 离职时间
     */
    private String dimissionTimeStr;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;
}

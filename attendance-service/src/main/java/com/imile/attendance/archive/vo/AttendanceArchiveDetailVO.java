package com.imile.attendance.archive.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AttendanceArchiveDetailVO implements Serializable {

    /**
     * 员工基本信息
     */
    private EmployeeBaseInfoVO employeeBaseInfo;

    /**
     * 考勤配置信息
     */
    private AttendanceRuleConfigVO attendanceRuleConfig;


    @Data
    public static class EmployeeBaseInfoVO {
        /**
         * 姓名
         */
        private String userName;

        /**
         * 账号
         */
        private String userCode;

        /**
         * 工作状态
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
        private String workStatus;

        /**
         * 工作状态描述
         */
        private String workStatusDesc;

        /**
         * 状态
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
        private String status;

        /**
         * 状态描述
         */
        private String statusDesc;

        /**
         * 用工类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
        private String employeeType;

        /**
         * 用工类型名称
         */
        private String employeeTypeDesc;

        /**
         * 是否司机
         */
        private Integer isDriver;

        /**
         * 是否派遣
         */
        private Integer isGlobalRelocation;

        /**
         * 部门ID
         */
        private Long deptId;

        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 岗位ID
         */
        private Long postId;

        /**
         * 岗位名称
         */
        private String postName;

        /**
         * 常驻国
         */
        private String locationCountry;

        /**
         * 常驻省
         */
        private String locationProvince;

        /**
         * 常驻市
         */
        private String locationCity;

        /**
         * 入职时间
         */
        private Date entryDate;

        /**
         * 离职时间
         */
        private Date dimissionDate;
    }


    @Data
    public static class AttendanceRuleConfigVO {
        /**
         * 班次性质
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CLASS_NATURE, ref = "classNatureDesc")
        private String classNature;

        /**
         * 班次性质描述
         */
        private String classNatureDesc;

        /**
         * 班次信息
         */
        private List<ClassVO> classVOList;

        /**
         * 日历ID
         */
        private Long calendarId;

        /**
         * 日历名称
         */
        private String calendarName;

        /**
         * 日历所属国
         */
        private String calendarCountry;

        /**
         * 打卡规则ID
         */
        private Long punchConfigId;

        /**
         * 打卡规则编码
         */
        private String punchConfigNo;

        /**
         * 打卡规则名称
         */
        private String punchConfigName;

        /**
         * 打卡规则所属国
         */
        private String punchConfigCountry;

        /**
         * 补卡规则ID
         */
        private Long reissueCardConfigId;

        /**
         * 补卡规则编码
         */
        private String reissueCardConfigNo;

        /**
         * 补卡规则名称
         */
        private String reissueCardConfigName;

        /**
         * 补卡规则所属国
         */
        private String reissueCardConfigCountry;

        /**
         * 加班规则ID
         */
        private Long overTimeConfigId;

        /**
         * 加班规则编码
         */
        private String overTimeConfigNo;

        /**
         * 加班规则名称
         */
        private String overTimeConfigName;

        /**
         * 加班规则所属国
         */
        private String overTimeConfigCountry;
    }

    /**
     * 班次信息
     */
    @Data
    public static class ClassVO {
        /**
         * 班次ID
         */
        private Long classId;
        /**
         * 班次名称
         */
        private String className;
    }

}

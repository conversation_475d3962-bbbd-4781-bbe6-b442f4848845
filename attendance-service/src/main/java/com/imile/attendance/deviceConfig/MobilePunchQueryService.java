package com.imile.attendance.deviceConfig;

import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileHistoryConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceGpsConfigMapstruct;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceMobileConfigMapstruct;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceWifiConfigMapstruct;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.util.BeanUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/28 
 * @Description 手机打卡查询配置
 */
@Service
public class MobilePunchQueryService {

    @Resource
    private AttendanceWifiConfigManage wifiConfigManage;
    @Resource
    private AttendanceGpsConfigManage gpsConfigManage;
    @Resource
    private AttendanceMobileConfigManage mobileConfigManage;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;


    /**
     * 获取所有的wifi配置
     */
    public List<AttendanceWifiConfigDTO> getAllWifiConfig() {
        List<AttendanceWifiConfigDO> allWifiConfigList = wifiConfigManage.getAllWifiConfig();
        return AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(allWifiConfigList);
    }

    /**
     * 获取所有的gps配置
     */
    public List<AttendanceGpsConfigDTO> getAllGpsConfig() {
        List<AttendanceGpsConfigDO> list = gpsConfigManage.getAllGpsConfig();
        return AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(list);
    }

    /**
     * 用户考勤手机查询(含打卡时间)
     */
    public List<AttendanceMobileConfigDTO> selectByUserCodeForDetail(String userCode) {
        List<AttendanceMobileConfigDO> mobileConfigDOS = mobileConfigManage.queryMobileConfigByUserCode(userCode);
        if (CollectionUtils.isEmpty(mobileConfigDOS)) {
            return Collections.emptyList();
        }
        List<Long> mobileConfigIds = mobileConfigDOS.stream()
                .map(AttendanceMobileConfigDO::getId)
                .collect(Collectors.toList());
        Map<Long, List<EmployeePunchRecordDO>> punchMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mobileConfigIds)) {
            List<EmployeePunchRecordDO> punchListByMobileConfigId = employeePunchRecordManage.getPunchListByMobileConfigId(mobileConfigIds);
            if (CollectionUtils.isNotEmpty(punchListByMobileConfigId)) {
                punchMap = punchListByMobileConfigId.stream()
                        .collect(Collectors.groupingBy(EmployeePunchRecordDO::getMobileConfigId));
            }
        }
        List<AttendanceMobileConfigDTO> mobileConfigDTOList = AttendanceMobileConfigMapstruct.INSTANCE.toAttendanceMobileConfigDTO(mobileConfigDOS);
        for (AttendanceMobileConfigDTO dto : mobileConfigDTOList) {
            List<EmployeePunchRecordDO> punchRecordDOS = punchMap.get(dto.getId());
            if (CollectionUtils.isNotEmpty(punchRecordDOS)) {
                dto.setPunchTime(punchRecordDOS.get(0).getPunchTime());
            }
        }
        return mobileConfigDTOList;
    }

    /**
     * 用户考勤手机历史记录查询
     */
    public List<AttendanceMobileHistoryConfigDTO> selectHistoryByUserCode(String userCode) {
        List<AttendanceMobileConfigDO> mobileConfigDOS = mobileConfigManage.queryHistoryMobileConfigByUserCode(userCode);
        if (CollectionUtils.isEmpty(mobileConfigDOS)) {
            return Collections.emptyList();
        }
        return AttendanceMobileConfigMapstruct.INSTANCE.toAttendanceMobileConfigHistoryDTO(mobileConfigDOS);
    }


    /**
     * 用户考勤手机查询(优化打卡用)
     */
    public List<AttendanceMobileConfigDTO> selectByUserCodeForPunch(String userCode) {
        return AttendanceMobileConfigMapstruct.INSTANCE.toAttendanceMobileConfigDTO(
                mobileConfigManage.queryMobileConfigByUserCode(userCode));
    }


}

package com.imile.attendance.abnormal.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-7
 * @version: 1.0
 */
@Data
public class DayAttendanceTimeParam {
    /**
     * 异常考勤记录ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long abnormalId;
}

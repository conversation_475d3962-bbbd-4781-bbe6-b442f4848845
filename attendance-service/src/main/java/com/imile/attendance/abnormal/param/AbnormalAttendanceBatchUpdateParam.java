package com.imile.attendance.abnormal.param;

import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-7
 * @version: 1.0
 */
@Data
public class AbnormalAttendanceBatchUpdateParam {

    /**
     * 异常考勤记录ID
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> abnormalIdList;

    /**
     * 更新类型 :AttendanceAbnormalOperationTypeEnum(请假/外勤/补卡不走这个请求，直接走对应的审批发起入口,只支持P,OFF,PH,异常确认)
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String updateType;

    /**
     * 原因说明：只有批量修改为正常的时候才传该字段（限制200字），其他情况不传数据库默认为空
     */
    private String reason;

    /**
     * 附件：批量修改为正常时选填，数据库默认为null
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 确认异常处理时间
     */
    private Date confirmTime;

}

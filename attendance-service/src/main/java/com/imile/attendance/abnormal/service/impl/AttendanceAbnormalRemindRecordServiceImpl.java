package com.imile.attendance.abnormal.service.impl;

import com.imile.attendance.abnormal.param.AbnormalRemindRecordAddParam;
import com.imile.attendance.abnormal.service.AttendanceAbnormalRemindRecordService;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceAbnormalRemindRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalRemindRecordQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.util.BeanUtils;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 异常发送提醒记录 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2024/10/22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AttendanceAbnormalRemindRecordServiceImpl implements AttendanceAbnormalRemindRecordService {

    private final AttendanceAbnormalRemindRecordDao abnormalRemindRecordDao;

    @Override
    public List<AttendanceAbnormalRemindRecordDO> listOnly(AbnormalRemindRecordQuery query) {
        return abnormalRemindRecordDao.listOnly(query);
    }

    @Override
    public void add(AbnormalRemindRecordAddParam param) {
        AttendanceAbnormalRemindRecordDO abnormalRemindRecordDO = BeanUtils.convert(param, AttendanceAbnormalRemindRecordDO.class);
        BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalRemindRecordDO);
        abnormalRemindRecordDao.save(abnormalRemindRecordDO);
    }
}

package com.imile.attendance.abnormal.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description: 每日异常提醒xxl-job传入参数
 * @author: han.wang
 * @createDate: 2024-10-17
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbnormalAttendanceDayReminderParam {
    /**
     * 常驻国列表
     */
    private String countryList;

    /**
     * 用户编码
     */
    private String userCodeList;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 当前日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentDate;

    /**
     * 发送方式(0: 自动 1: 手动)
     */
    private Integer sendType;


}

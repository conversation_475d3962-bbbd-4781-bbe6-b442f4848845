package com.imile.attendance.abnormal.dto;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeAbnormalAttendanceQueryDTO extends ResourceQuery {

    /**
     * 用户id列表
     */
    private List<Long> userIds;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门id列表
     */
    private List<Long> deptIds;

    /**
     * 异常状态
     */
    private List<String> statusList;
    /**
     * 员工类型
     */
    private String employeeType;
    /**
     * 员工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常类型列表
     */
    private List<String> abnormalTypeList;

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 截止时间
     */
    private Date endDate;

    /**
     * 司机:driver 仓内:warehouse 办公室员工:office  已处理界面不用传，全部类型都需要查出来
     */
    private List<String> staffTypes;

    private String country;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    private Boolean isChooseDept = Boolean.FALSE;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 工作网点
     */
    private Long ocId;


    /**
     * 工作网点, 多选
     */
    private List<Long> ocIdList;

    /**
     * 工作供应商
     */
    private Long vendorId;

    /**
     * 工作供应商 多选
     */
    private List<Long> vendorIdList;

    /**
     * 查询来源 1:仓内异常查询
     */
    private Integer source;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 用工形式
     */
    private String employmentForm;
}

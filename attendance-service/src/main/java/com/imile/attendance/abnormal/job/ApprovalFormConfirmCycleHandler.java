package com.imile.attendance.abnormal.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.form.FormAttrUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.idwork.IdWorkerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/3 
 * @Description 用户考勤单据确认周期监控
 */
@Slf4j
@Component
public class ApprovalFormConfirmCycleHandler {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private RpcBpmApprovalClient rpcBpmApprovalClient;


    @XxlJob(BusinessConstant.JobHandler.APPROVAL_FORM_CONFIRM_CYCLE_HANDLER)
    public ReturnT<String> approvalFormConfirmCycleHandler(String content) {

        ApprovalFormConfirmCycleHandler.HandlerParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, ApprovalFormConfirmCycleHandler.HandlerParam.class) :
                new ApprovalFormConfirmCycleHandler.HandlerParam();

        List<AttendanceUser> userList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodes().split(","));
            userList = userService.listUsersByUserCodes(userCodeList);
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            List<String> originCountryList = Arrays.asList(param.getCountryList().split(","));
            UserDaoQuery query = UserDaoQuery.builder()
                    .locationCountryList(originCountryList)
                    .build();
            // 这边不过滤在职状态，由于UserDaoQuery设置了在职状态，外面设置为空
            query.setWorkStatus("");
            userList = userService.listUsersByQuery(query);
            //userInfoDOList = hrmsUserInfoManage.selectUserInfoByCompanyIdList(originCountryList);
        }
        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(Collections.singletonList(FormStatusEnum.IN_REVIEW.getCode()));
        applicationFormQuery.setFromTypeList(Arrays.asList(FormTypeEnum.LEAVE.getCode(),
                FormTypeEnum.OUT_OF_OFFICE.getCode(), FormTypeEnum.REISSUE_CARD.getCode()));
        List<AttendanceFormDO> formDOList = attendanceFormManage.selectForm(applicationFormQuery);
        if (CollectionUtils.isEmpty(formDOList)) {
            XxlJobLogger.log("没有单据,不处理");
            return ReturnT.SUCCESS;
        }

        List<Long> formIdList = formDOList.stream()
                .map(AttendanceFormDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> attrDOList = attendanceFormManage.selectFormAttrByFormIdLit(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> attrMap = attrDOList.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));

        List<AttendanceFormRelationDO> relationDOList = attendanceFormManage.selectRelationByFormIdList(formIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        List<Long> abnormalIdList = relationDOList.stream()
                .map(AttendanceFormRelationDO::getRelationId)
                .collect(Collectors.toList());
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectByIdList(abnormalIdList);
        Map<Long, List<EmployeeAbnormalAttendanceDO>> abnormalMap = abnormalAttendanceDOList.stream()
                .collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getId));

        List<AttendanceFormDO> filterFormList = new ArrayList<>();
        log.info("approvalFormConfirmCycleHandler|applicationFormDOList:{}", formDOList.size());
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> formAttrList = attrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrList)) {
                continue;
            }
            //获取用户的考勤周期配置
            AttendanceCycleConfigDO attendanceCycleConfigDO = null;
            try {
                attendanceCycleConfigDO = attendanceCycleConfigService.getUserAttendanceCycleConfig(formDO.getUserId());
            } catch (Exception e) {
                log.info("approvalFormConfirmCycleHandler|用户的考勤周期配置不存在,userId:{}", formDO.getUserId());
                continue;
            }
            if (ObjectUtil.isNull(attendanceCycleConfigDO)) {
                log.info("approvalFormConfirmCycleHandler attendanceCycleConfigDO not exist,userId:{}", formDO.getUserId());
                continue;
            }
            Date nowDate = new Date();
            Boolean tag = true;
            Date leaveStartDate = null;
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                AttendanceFormAttrDO leaveStartDateAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.leaveStartDate);
                if (Objects.isNull(leaveStartDateAttr)) {
                    continue;
                }
                leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateAttr.getAttrValue());
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                AttendanceFormAttrDO outOfOfficeStartDateAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
                if (Objects.isNull(outOfOfficeStartDateAttr)) {
                    continue;
                }
                leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateAttr.getAttrValue());
            }
            //补卡
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD.getCode())) {
                AttendanceFormAttrDO reissueCardDayIdAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.reissueCardDayId);
                if (Objects.isNull(reissueCardDayIdAttr)) {
                    continue;
                }
                leaveStartDate = DateUtil.parse(reissueCardDayIdAttr.getAttrValue(), "yyyyMMdd");
            }
            if (leaveStartDate == null) {
                continue;
            }
            tag = confirmCycleCheck(nowDate, attendanceCycleConfigDO, leaveStartDate);
            log.info("approvalFormConfirmCycleHandler tag：{}", tag);
            if (tag) {
                continue;
            }
            //超过期限了
            filterFormList.add(formDO);
        }
        log.info("approvalFormConfirmCycleHandler|filterFormList:{}", filterFormList.size());
        List<AttendanceFormDO> updateFormList = new ArrayList<>();
        List<AttendanceFormAttrDO> addAttrList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> addRecordList = new ArrayList<>();
        for (AttendanceFormDO formDO : filterFormList) {
            //需要通知审批中心
            if (formDO.getApprovalId() != null) {
                try {
                    rpcBpmApprovalClient.backApply(formDO.getApprovalId());
                } catch (Exception e) {
                    log.info("approvalFormConfirmCycleHandler|单据的approvalId不存在,applicationCode:{}", formDO.getApplicationCode());
                    continue;
                }
            }
            formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(formDO);
            updateFormList.add(formDO);
            //加个备注
            AttendanceFormAttrDO attendanceFormAttrDO = new AttendanceFormAttrDO();
            attendanceFormAttrDO.setId(IdWorkerUtil.getId());
            attendanceFormAttrDO.setFormId(formDO.getId());
            attendanceFormAttrDO.setAttrKey(ApplicationFormAttrKeyEnum.terminatedReason.getCode());
            attendanceFormAttrDO.setAttrValue("用户考勤单据确认周期监控取消的单据");
            BaseDOUtil.fillDOInsertByUsrOrSystem(attendanceFormAttrDO);
            addAttrList.add(attendanceFormAttrDO);

            //查看有没有关联异常，如果有，就确认异常
            List<AttendanceFormRelationDO> existRelationDOList = relationDOList.stream()
                    .filter(item -> item.getFormId().equals(formDO.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existRelationDOList)) {
                continue;
            }
            List<EmployeeAbnormalAttendanceDO> existAbnormal = abnormalMap.get(existRelationDOList.get(0).getRelationId());
            if (CollectionUtils.isEmpty(existAbnormal)) {
                continue;
            }
            //需要确认异常
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = existAbnormal.get(0);
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
            updateAbnormalList.add(abnormalAttendanceDO);

            //添加对异常的操作记录
            EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
            abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
            abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode());
            BaseDOUtil.fillDOInsert(abnormalOperationRecordDO);
            addRecordList.add(abnormalOperationRecordDO);
        }
        log.info("approvalFormConfirmCycleHandler|updateFormList:{},updateAbnormalList:{}", updateFormList.size(), updateAbnormalList.size());
        //落库
        abnormalAttendanceManage.updateApprovalFormConfirmCycle(updateFormList, addAttrList, updateAbnormalList, addRecordList);
        return ReturnT.SUCCESS;
    }

    /**
     * 确认周期校验
     *
     * @param nowDate                     当前时间
     * @param attendanceCycleConfigDO 考勤周期配置
     * @param specificTime                指定日期
     * @return Boolean
     */
    private Boolean confirmCycleCheck(Date nowDate, AttendanceCycleConfigDO attendanceCycleConfigDO, Date specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = DateHelper.getDayId(specificTime);

        // 如果参数不符合规则，直接返回true
        if (ObjectUtil.isNull(nowDate) || ObjectUtil.isNull(attendanceCycleConfigDO) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleStart()) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleEnd()) ||
                ObjectUtil.isNull(attendanceCycleConfigDO.getAbnormalExpired())) {
            return true;
        }

        Integer cycleType = attendanceCycleConfigDO.getCycleType();
        String cycleEnd = attendanceCycleConfigDO.getCycleEnd();

        CycleTypeEnum cycleTypeEnum = ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType()) ?
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name()) :
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            return true;
        }

        Integer abnormalExpired = cycleTypeEnum.getActualAbnormalExpired(
                nowDate,
                attendanceCycleConfigDO.getCycleStart(),
                attendanceCycleConfigDO.getCycleEnd(),
                attendanceCycleConfigDO.getAbnormalExpired()
        );

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = DateHelper.getDayId(offsetCycleEndDate);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            return false;
        }
        return true;
    }


    @Data
    private static class HandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
    }

}

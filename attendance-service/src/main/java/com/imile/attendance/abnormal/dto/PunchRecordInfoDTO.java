package com.imile.attendance.abnormal.dto;

import com.imile.attendance.rule.vo.PunchClassItemConfigVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PunchRecordInfoDTO {
    /**
     * 打卡规则id
     */
    private Long punchConfigId;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;


    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     */
    private String punchConfigType;

    /**
     * 当前班次 出勤时长
     */
    private BigDecimal attendanceHours;


    /**
     * 当前班次 法定工作时长
     */
    private BigDecimal legalWorkingHours;


    /**
     * 当天班次对应的所有时刻信息
     */
    private List<PunchClassItemConfigVO> itemConfigDOList;

    /**
     * 当前异常所属的时刻ID
     */
    private Long punchClassItemConfigId;

    /**
     * 当前时刻的实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;
}

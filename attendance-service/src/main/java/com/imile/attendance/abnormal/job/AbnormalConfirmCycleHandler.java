package com.imile.attendance.abnormal.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.idwork.IdWorkerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/3 
 * @Description 异常考勤确认周期监控
 */
@Slf4j
@Component
public class AbnormalConfirmCycleHandler {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private RpcBpmApprovalClient rpcBpmApprovalClient;


    @XxlJob(BusinessConstant.JobHandler.ABNORMAL_CONFIRM_CYCLE_HANDLER)
    public ReturnT<String> abnormalConfirmCycleHandler(String content) {

        AbnormalConfirmCycleHandler.HandlerParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, AbnormalConfirmCycleHandler.HandlerParam.class) :
                new AbnormalConfirmCycleHandler.HandlerParam();

        List<AttendanceUser> userList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodes().split(","));
            userList = userService.listByUserCodes(userCodeList);
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            List<String> originCountryList = Arrays.asList(param.getCountryList().split(","));
            UserDaoQuery query = UserDaoQuery.builder()
                    .locationCountryList(originCountryList)
                    .build();
            // 这边不过滤在职状态，由于UserDaoQuery设置了在职状态，外面设置为空
            query.setWorkStatus("");
            userList = userService.listUsersByQuery(query);
        }
        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        List<AttendanceFormDO> updateFormList = new ArrayList<>();
        List<AttendanceFormAttrDO> addAttrList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> addRecordList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        // 全量查询异常考勤，该为批量查询
        int currentPage = 1;
        int pageSize = 1000;
        Page<EmployeeAbnormalAttendanceDO> page = PageHelper.startPage(currentPage, pageSize, true);
        PageInfo<EmployeeAbnormalAttendanceDO> pageInfo = page.doSelectPageInfo(
                () -> abnormalAttendanceManage.selectAbnormalByUserIdList(userIdList));
        // 总记录数
        List<EmployeeAbnormalAttendanceDO> pageInfoList = pageInfo.getList();
        if (CollUtil.isNotEmpty(pageInfoList)) {
            log.info("abnormalConfirmCycleHandler pageInfoList:{}", pageInfoList.size());
            handlerAbnormalAttendance(pageInfoList, updateAbnormalList, addRecordList, updateFormList, addAttrList);
        }
        log.info("abnormalConfirmCycleHandler currentPage:{},pageSize:{},total:{},pages:{}", currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());

        while (currentPage < pageInfo.getPages()) {
            log.info("进入while循环");
            currentPage++;
            log.info("currentPage：{}，pages：{}", currentPage, pageInfo.getPages());

            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(
                    () ->  abnormalAttendanceManage.selectAbnormalByUserIdList(userIdList));
            pageInfoList = pageInfo.getList();
            if (CollUtil.isNotEmpty(pageInfoList)) {
                log.info("while循环：pageInfoList size:{},pageInfoList{}", pageInfoList.size(), JSON.toJSONString(pageInfoList));
                handlerAbnormalAttendance(pageInfoList, updateAbnormalList, addRecordList, updateFormList, addAttrList);
            }
            log.info("while循环：pageInfoList | currentPage:{},pageSize:{},total:{}", currentPage, pageSize, pageInfo.getTotal());
            log.info("currentPage {}，while循环结束", currentPage);
        }

        log.info("abnormalConfirmCycleHandler|updateFormList:{},addAttrList:{},updateAbnormalList:{},addRecordList:{}", updateFormList.size(), addAttrList.size(), updateAbnormalList.size(), addRecordList.size());
        log.info("abnormalConfirmCycleHandler|耗时：{}", System.currentTimeMillis() - startTime);
        //落库
        abnormalAttendanceManage.updateApprovalFormConfirmCycle(updateFormList, addAttrList, updateAbnormalList, addRecordList);
        return ReturnT.SUCCESS;
    }

    /**
     * 处理异常考勤数据
     *
     * @param abnormalAttendanceDOList 异常考勤数据
     * @param updateAbnormalList       待更新的异常考勤数据
     * @param addRecordList            待添加的异常操作记录
     * @param updateFormList           待更新的审批单
     * @param addAttrList              待添加的审批单属性
     */
    private void handlerAbnormalAttendance(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList,
                                           List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                           List<EmployeeAbnormalOperationRecordDO> addRecordList,
                                           List<AttendanceFormDO> updateFormList,
                                           List<AttendanceFormAttrDO> addAttrList) {

        List<Long> abnormalIdList = abnormalAttendanceDOList.stream()
                .map(EmployeeAbnormalAttendanceDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormRelationDO> relationDOList = attendanceFormManage.selectRelationByRelationIdList(abnormalIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());

        List<Long> formIdList = relationDOList.stream()
                .map(AttendanceFormRelationDO::getFormId)
                .collect(Collectors.toList());

        List<AttendanceFormDO> formDOList = attendanceFormManage.selectByIdList(formIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode()))
                .collect(Collectors.toList());

        log.info("abnormalConfirmCycleHandler|abnormalAttendanceDOList:{}", abnormalAttendanceDOList.size());
        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            //获取用户的考勤周期
            AttendanceCycleConfigDO attendanceCycleConfigDO = null;
            try {
                attendanceCycleConfigDO = attendanceCycleConfigService.getUserAttendanceCycleConfig(abnormalAttendanceDO.getUserId());
            } catch (Exception e) {
                log.info("abnormalConfirmCycleHandler|用户的薪资方案不存在,userId:{}", abnormalAttendanceDO.getUserId());
                continue;
            }
            if (ObjectUtil.isNull(attendanceCycleConfigDO)) {
                log.info("abnormalConfirmCycleHandler attendanceCycleConfigDO not exist,userId:{}", abnormalAttendanceDO.getUserId());
                continue;
            }
            Date nowDate = new Date();
            // 获取是否需要确认标志
            Boolean tag = confirmCycleCheck(nowDate, attendanceCycleConfigDO,
                    DateUtil.beginOfDay(DateUtil.parse(abnormalAttendanceDO.getDayId().toString(), "yyyyMMdd")));
            log.info("abnormalConfirmCycleHandler tag：{}", tag);
            if (tag) {
                continue;
            }
            //待处理/已驳回/审核中
            if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                    || StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())
                    || StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                String oldStatus = abnormalAttendanceDO.getStatus();
                //需要确认异常
                abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
                updateAbnormalList.add(abnormalAttendanceDO);
                //添加对异常的操作记录
                EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
                abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
                abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
                abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode());
                BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);
                addRecordList.add(abnormalOperationRecordDO);

                if (!StringUtils.equalsIgnoreCase(oldStatus, AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                    continue;
                }

                //针对审核中的特殊处理
                List<AttendanceFormRelationDO> existAbnormalRelationDOList = relationDOList.stream()
                        .filter(item -> item.getRelationId().equals(abnormalAttendanceDO.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existAbnormalRelationDOList)) {
                    continue;
                }
                List<AttendanceFormDO> existAbnormalFormDOList = formDOList.stream()
                        .filter(item -> item.getId().equals(existAbnormalRelationDOList.get(0).getFormId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existAbnormalFormDOList)) {
                    continue;
                }
                AttendanceFormDO formDO = existAbnormalFormDOList.get(0);
                formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(formDO);
                updateFormList.add(formDO);
                //加个备注
                AttendanceFormAttrDO attendanceFormAttrDO = new AttendanceFormAttrDO();
                attendanceFormAttrDO.setId(IdWorkerUtil.getId());
                attendanceFormAttrDO.setFormId(formDO.getId());
                attendanceFormAttrDO.setAttrKey(ApplicationFormAttrKeyEnum.terminatedReason.getCode());
                attendanceFormAttrDO.setAttrValue("异常考勤确认周期监控取消的单据");
                BaseDOUtil.fillDOInsertByUsrOrSystem(attendanceFormAttrDO);
                addAttrList.add(attendanceFormAttrDO);
                if (formDO.getApprovalId() != null) {
                    try {
                        rpcBpmApprovalClient.backApply(formDO.getApprovalId());
                    } catch (Exception e) {
                        log.info("abnormalConfirmCycleHandler|单据的approvalId不存在,applicationCode:{}", formDO.getApplicationCode());
                    }
                }
            }
        }



    }

    private Boolean confirmCycleCheck(Date nowDate, AttendanceCycleConfigDO attendanceCycleConfigDO, DateTime specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = DateHelper.getDayId(specificTime);

        // 如果参数不符合规则，直接返回true
        if (ObjectUtil.isNull(nowDate) || ObjectUtil.isNull(attendanceCycleConfigDO) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleStart()) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleEnd()) ||
                ObjectUtil.isNull(attendanceCycleConfigDO.getAbnormalExpired())) {
            return true;
        }

        Integer cycleType = attendanceCycleConfigDO.getCycleType();
        String cycleEnd = attendanceCycleConfigDO.getCycleEnd();

        CycleTypeEnum cycleTypeEnum = ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType()) ?
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name()) :
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            return true;
        }

        Integer abnormalExpired = cycleTypeEnum.getActualAbnormalExpired(
                nowDate,
                attendanceCycleConfigDO.getCycleStart(),
                attendanceCycleConfigDO.getCycleEnd(),
                attendanceCycleConfigDO.getAbnormalExpired()
        );

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = DateHelper.getDayId(offsetCycleEndDate);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            return false;
        }
        return true;
    }


    @Data
    private static class HandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
    }
}

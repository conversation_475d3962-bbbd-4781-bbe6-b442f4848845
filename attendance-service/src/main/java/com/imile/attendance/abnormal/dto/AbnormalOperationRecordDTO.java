package com.imile.attendance.abnormal.dto;

import com.imile.attendance.user.dto.AttachmentDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-1
 * @version: 1.0
 */
@Data
public class AbnormalOperationRecordDTO {

    /**
     * 处理内容
     */
    private String operationContent;

    /**
     * 操作类型(请假/补卡/外勤/确认异常)
     */
    private String operationType;

    /**
     * 处理结果
     */
    private String operationStatus;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 审批单号
     */
    private String applicationCode;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}

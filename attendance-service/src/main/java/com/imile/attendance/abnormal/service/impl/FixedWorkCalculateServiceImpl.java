package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.DayItemInfoDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 固定班次打卡考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "FixedWorkCalculateServiceImpl")
public class FixedWorkCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {

    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FIXED_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        UserInfoDO user = calculateContext.getUser();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 固定班次计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();
        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = calculateContext.getAttendanceEmployeeDetailDOList().stream()
                .map(AttendanceEmployeeDetailDO::getFormId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //需要先将改天的所有审批通过的请假/外勤拼接起来，一个时刻可以有多个审批通过的假期，每次请10分钟，请5次，就有5个单据
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = new ArrayList<>();
        dayFormInfoBuild(handlerFormDTOList, usedFormIdList, calculateContext.getUserPassFormBOList());
        handlerFormDTOList = handlerFormDTOList.stream().sorted(Comparator.comparing(DayAttendanceHandlerFormDTO::getStartTime)).collect(Collectors.toList());

        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        UserAttendancePunchConfigDTO attendancePunchConfigDTO = calculateContext.getUserAttendancePunchConfigDTOList().get(0);
        List<PunchClassItemConfigDTO> classItemConfigDOList = attendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList();
        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigDOList);
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            DayItemConfigDateDTO itemConfigDate = buildDayItemConfigDateDTO(user, itemConfigDO, itemConfigDOList, calculateContext.getPunchRecordDOList(), calculateHandlerDTO);
            if (Objects.isNull(itemConfigDate)) {
                continue;
            }

            log.info("attendanceCalculate userCode:{}, date:{},sortNo:{},itemId:{},当天的班次的具体信息 itemConfigDate:{}",
                    user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), itemConfigDO.getSortNo(), itemConfigDO.getId(), JSON.toJSONString(itemConfigDate));

            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            //计算单据中请假或外勤的出勤时长
            BigDecimal usedMinutes = calculateLeaveHandler(calculateContext, addEmployeeDetailDOList, handlerFormDTOList, itemConfigDate, filterFormDTOList);
            if (usedMinutes == null) {
                continue;
            }
            log.info("attendanceCalculate userCode:{}, date:{},itemTotalMinutes:{},usedMinutes:{}", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), itemConfigDate.getItemTotalMinutes(), usedMinutes);

            //当前时刻完全请假
            if ((itemConfigDate.getItemTotalMinutes().subtract(usedMinutes)).compareTo(BigDecimal.ZERO) < 1) {
                log.info("attendanceCalculate userCode:{}, date:{},当天完全请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                continue;
            }

            //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
            List<UserPunchRecordBO> itemPunchRecordList = getEffectiveUserPunchRecord(itemConfigDate.getEarliestPunchInTime(), itemConfigDate.getLatestPunchOutTime(), calculateContext.getPunchRecordDOList());

            //情况1:当天没有打卡时间
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次没有打卡时间", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                normalPunchNoPunchTimeHandler(user, calculateHandlerDTO, itemConfigDate.getPunchInTime(), itemConfigDate.getPunchOutTime(), calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(),
                        attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), addAbnormalAttendanceDOList);
                continue;
            }

            //情况2:当天完全没请假，看打卡记录
            if (CollectionUtils.isEmpty(filterFormDTOList)) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次有打卡记录,完全没请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                normalPunchHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList,
                        calculateContext.getPunchConfigDO().getId(), attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(),
                        itemConfigDate, itemConfigDate.getItemTotalMinutes().subtract(usedMinutes));
                continue;
            }

            //情况3:当天存在请假  有一条打卡记录
            if (itemPunchRecordList.size() == 1) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次有一条打卡记录且存在请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                singlePunchRecordHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, calculateContext.getPunchConfigDO().getId(),
                        attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(), itemConfigDate.getPunchInTime(),
                        itemConfigDate.getPunchOutTime(), itemConfigDate.getLatestPunchInTime());
                continue;
            }
            //情况4: 按照下面三个区间考虑所有情况
            //最早上班时间-上班时间
            //上班时间-下班时间
            //下班时间到-最晚下班时间
            log.info("attendanceCalculate userCode:{}, date:{},  当天固定班次有多条打卡记录且存在请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            leaveDayBatchHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList, filterFormDTOList,
                    calculateContext.getPunchConfigDO().getId(), attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(),
                    itemConfigDate, itemConfigDate.getItemTotalMinutes().subtract(usedMinutes));
        }

        //过滤审批中的异常
        addAbnormalAttendanceDOList = filterAbnormalAttendanceList(calculateContext.getUserAbnormalAttendanceDOList(), addAbnormalAttendanceDOList);
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
    }

    /**
     * 当天没有打卡记录
     */
    private void normalPunchNoPunchTimeHandler(UserInfoDO user,
                                               AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                               Date punchInTime,
                                               Date punchOutTime,
                                               String attendanceType,
                                               Long punchConfigId,
                                               Long classId,
                                               Long itemConfigId,
                                               List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //2条异常，上下班都缺卡
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

        abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
        EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
    }

    /**
     * 时刻没有请假，正常打卡
     */
    private void normalPunchHandler(UserInfoDO user,
                                    AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                    List<UserPunchRecordBO> itemPunchRecordList,
                                    List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                    List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                    Long punchConfigId,
                                    Long classId,
                                    Long itemConfigId,
                                    String attendanceType,
                                    DayItemConfigDateDTO itemConfigDateDTO,
                                    BigDecimal itemTotalMinutes) {
        Date punchInTime = itemConfigDateDTO.getPunchInTime();
        Date punchOutTime = itemConfigDateDTO.getPunchOutTime();
        Date latestPunchInTime = itemConfigDateDTO.getLatestPunchInTime();

        //一次打卡
        if (itemPunchRecordList.size() == 1) {
            singlePunchRecordHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, punchConfigId,
                    classId, itemConfigId, attendanceType, punchInTime, punchOutTime, latestPunchInTime);
            return;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }
        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                    null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }
            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE))
                    .compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            //还是早退，注意这里，下班补卡时间不再是班次的下班时间了，应该是弹性时间后移
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班肯定迟到
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(abnormalAttendanceDO);

        //下班也早退
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
            AbnormalExtendDTO leaveEarlyAbnormalExtendDTO = new AbnormalExtendDTO();
            leaveEarlyAbnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            leaveEarlyAbnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO leaveEarlyAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(leaveEarlyAbnormalExtendDTO));
            addAbnormalAttendanceDOList.add(leaveEarlyAbnormalAttendanceDO);
        }
    }


    /**
     * 存在请假和多条打卡记录
     */
    private void leaveDayBatchHandler(UserInfoDO user,
                                      AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                      List<UserPunchRecordBO> itemPunchRecordList,
                                      List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                      List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                      List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                      Long punchConfigId,
                                      Long classId,
                                      Long itemConfigId,
                                      String attendanceType,
                                      DayItemConfigDateDTO itemConfigDateDTO,
                                      BigDecimal itemTotalMinutes) {

        Date earliestPunchInTime = itemConfigDateDTO.getEarliestPunchInTime();
        Date punchInTime = itemConfigDateDTO.getPunchInTime();
        Date latestPunchInTime = itemConfigDateDTO.getLatestPunchInTime();
        Date punchOutTime = itemConfigDateDTO.getPunchOutTime();
        Date latestPunchOutTime = itemConfigDateDTO.getLatestPunchOutTime();
        Date restStartTime = itemConfigDateDTO.getRestStartTime();
        Date restEndTime = itemConfigDateDTO.getRestEndTime();
        long betweenMinutes = itemConfigDateDTO.getBetweenMinutes();

        // [最早上班时间-上班时间)
        List<UserPunchRecordBO> punchBeforeCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(earliestPunchInTime) > -1
                        && o.getFormatPunchTime().compareTo(punchInTime) < 0)
                .collect(Collectors.toList());

        //[上班时间-下班时间]
        List<UserPunchRecordBO> punchBetweenCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchInTime) > -1
                        && o.getFormatPunchTime().compareTo(punchOutTime) < 1)
                .collect(Collectors.toList());

        //(下班时间-最晚下班时间]
        List<UserPunchRecordBO> punchAfterCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchOutTime) > 0
                        && o.getFormatPunchTime().compareTo(latestPunchOutTime) < 1)
                .collect(Collectors.toList());

        List<DayItemInfoDTO> dayItemInfoList = new ArrayList<>();
        //根据打卡记录过滤单据中外勤或请假的时长计算得到的打卡出勤时长
        BigDecimal presentMinutes;
        //打卡时间全部在正规上下班中 需要考虑打卡时间和申请单据的时间交集部分
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            //整个时段内，除去请假外的打卡记录的区间
            multiplePunchHandler(dayItemInfoList, filterFormDTOList, punchBetweenCardList);
            //打卡时间段去除休息时间段得到的出勤时长
            presentMinutes = restPunchHandler(dayItemInfoList, restStartTime, restEndTime);
            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }
            //早退
            if (punchBetweenCardList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(punchBetweenCardList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }

            //迟到并且早退
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);

            AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
            lateAbnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(0).getFormatPunchTime());
            lateAbnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
            addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
            return;
        }

        //开始时间和中间时间为空,直接是上班卡未打异常
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //结束时间和中间时间为空,直接是下班卡未打异常
        if (CollectionUtils.isEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //下班未打卡
        if (CollectionUtils.isNotEmpty(punchBeforeCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            Date beginPunchTime = punchInTime;
            Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            for (int i = 0; i < filterFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                dayItemInfoDTO.setEndItemTime(filterFormDTOList.get(i).getStartTime());
                dayItemInfoList.add(dayItemInfoDTO);
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    beginPunchTime = filterFormDTOList.get(i).getEndTime();
                    if (i == filterFormDTOList.size() - 1) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }

            //再去和休息时间比较
            presentMinutes = restPunchHandler(dayItemInfoList, restStartTime, restEndTime);

            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            //早退
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班未打卡
        if (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchBeforeCardList)) {
            Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            Date endPunchTime = punchOutTime;
            if (betweenMinutes != 0) {
                // 说明存在弹性时长，这边计算用户应该的的下班打卡时间（应该就是正常上下班的时间，包含弹性时长）
                Date shouldEndPunchTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                // 用户真实下班打卡时间
                Date lastPunchOutTime = punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime();
                // 获取最新的下班时间，判断是否大于弹性下班时长，大于等于的话，取弹性下班时间，否则取正常下班时长
                if (lastPunchOutTime.compareTo(shouldEndPunchTime) > -1) {
                    endPunchTime = shouldEndPunchTime;
                }
            }
            for (int i = filterFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(filterFormDTOList.get(i).getEndTime());
                dayItemInfoDTO.setEndItemTime(endPunchTime);
                dayItemInfoList.add(dayItemInfoDTO);

                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    endPunchTime = filterFormDTOList.get(i).getStartTime();
                    if (i == 0) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }

            //再去和休息时间比较
            presentMinutes = restPunchHandler(dayItemInfoList, restStartTime, restEndTime);

            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            if (punchBetweenCardList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) > 0) {
                //迟到
                AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
                lateAbnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(0).getFormatPunchTime());
                lateAbnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
                addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
                return;
            }

            AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
            lateAbnormalExtendDTO.setActualPunchTime(punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime());
            // 这边应该是使用上下班时间间隔作为偏移量
            lateAbnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(punchBetweenCardList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
            EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
            addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
            return;

        }

        //全部打卡,（或者前后都有打卡，就中间没打卡）没有异常，就看P的时间
        if ((CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))
                || (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))) {
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                    null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
        }
    }


    /**
     * 根据打卡时间和单据中时间计算组装不含请假或外勤的每一段时间
     *
     * @param dayItemInfoList      打卡记录不包含单据时间范围的时间段集合
     * @param handlerFormDTOList   当日有效单据集合
     * @param punchBetweenCardList 打卡时间集合
     */
    public void multiplePunchHandler(List<DayItemInfoDTO> dayItemInfoList,
                                     List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                     List<UserPunchRecordBO> punchBetweenCardList) {
        Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
        Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
        //比较请假时间和单据起始截止打卡时间的交集
        //整体在请假时间左边，没有交集
        if (endPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            return;
        }

        //整体在请假时间右边，没有交集
        if (beginPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 第一种情况：
         * 若单据只有一段 9:00 - 17:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 9:00 , 17:00 - 18:00;
         * 若单据只有一段 10:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 10:00 , 18:00 - 18:00;
         * 若单据只有一段 09:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 09:00 , 18:00 - 18:00;
         *
         * 第二种情况：
         * 若单据存在多段 9:00 - 11:00 , 12:00 - 14:00 , 15:00 - 18:00
         * 则dayItemInfoList 包含4段信息 9:00 - 9:00 , 11:00 - 12:00, 14:00 - 15:00, 18:00 - 18:00;
         *
         */
        //打卡完全包含请假（可能完全包含）
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1
                && endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            List<Date> punchTimeList = new ArrayList<>();
            punchTimeList.add(beginPunchTime);
            for (DayAttendanceHandlerFormDTO formDTO : handlerFormDTOList) {
                punchTimeList.add(formDTO.getStartTime());
                punchTimeList.add(formDTO.getEndTime());
            }
            punchTimeList.add(endPunchTime);
            for (int i = 0; i < punchTimeList.size(); ) {
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(punchTimeList.get(i));
                i = i + 1;
                dayItemInfoDTO.setEndItemTime(punchTimeList.get(i));
                i = i + 1;
                dayItemInfoList.add(dayItemInfoDTO);
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段说明当天完全请假;
         *
         * 若单据存在多段 6:00 - 8:00 , 10:00 - 13:00 , 15:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 10:00 , 13:00 - 15:00;
         *
         * 若单据存在多段 6:00 - 11:00 , 13:00 - 19:00;
         * 则dayItemInfoList 包含1段信息 11:00 - 13:00;
         *
         */
        //请假完全包含打卡
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) > -1
                && endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) < 1) {
            if (handlerFormDTOList.size() == 1) {
                return;
            }
            for (int i = 0; i < handlerFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    return;
                }
                int k = i + 1;
                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    beginPunchTime = handlerFormDTOList.get(i).getEndTime();
                    if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoDTO);
                        return;
                    }
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                    dayItemInfoList.add(dayItemInfoDTO);
                    beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                    continue;
                }

                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) > -1) {
                    if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoDTO);
                        return;
                    }
                    if (beginPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                        dayItemInfoList.add(dayItemInfoDTO);
                        beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                        continue;
                    }
                    beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                }
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段 10:00 - 19:00;
         * 则dayItemInfoList 包含1段信息 9:00 - 10:00;
         *
         * 若单据存在多段 11:00 - 12:00 , 14:00 - 19:00;
         * 则dayItemInfoList 包含2段信息 09:00 - 11:00 , 12:00 - 14:00;
         *
         * 若单据存在多段 10:00 - 11:00 , 13:00 - 15:00, 19:00 - 20:00;
         * 则dayItemInfoList 包含3段信息 09:00 - 10:00 , 11:00 - 13:00 , 15:00 - 18:00;
         *
         */
        //最早打卡时间在请假开始时间之前
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(0).getStartTime());
            dayItemInfoList.add(dayItemInfoDTO);
            for (int i = 0; i < handlerFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    return;
                }
                int k = i + 1;
                if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                    DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                    betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(i).getEndTime());
                    betweenDayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(betweenDayItemInfoDTO);
                    return;
                }
                DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(i).getEndTime());
                betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                dayItemInfoList.add(betweenDayItemInfoDTO);
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段 6:00 - 15:00;
         * 则dayItemInfoList 包含1段信息 15:00 - 18:00;
         *
         * 若单据存在多段 6:00 - 8:00 , 9:00 - 13:00;
         * 则dayItemInfoList 包含1段信息 13:00 - 18:00;
         *
         * 若单据存在多段 6:00 - 10:00 , 13:00 - 15:00;
         * 则dayItemInfoList 包含2段信息 10:00 - 13:00 , 15:00 - 18:00;
         *
         */
        //最晚打卡时间在请假结束时间之后
        if (endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime());
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            for (int i = handlerFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getStartTime()) > -1) {
                    return;
                }
                int k = i - 1;
                if (beginPunchTime.compareTo(handlerFormDTOList.get(k).getEndTime()) > -1) {
                    DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                    betweenDayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(i).getStartTime());
                    dayItemInfoList.add(betweenDayItemInfoDTO);
                    return;
                }
                DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(k).getEndTime());
                betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(i).getStartTime());
                dayItemInfoList.add(betweenDayItemInfoDTO);
            }
        }
    }


    /**
     * 根据申请单据的时间计算员工当日已出勤时长
     *
     * @param dayItemInfoList 打卡记录不包含单据时间范围的时间段集合
     * @param restStartTime   休息开始时间
     * @param restEndTime     休息结束时间
     * @return 员工已出勤时长
     */
    public BigDecimal restPunchHandler(List<DayItemInfoDTO> dayItemInfoList, Date restStartTime, Date restEndTime) {
        BigDecimal presentMinutes = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(dayItemInfoList)) {
            return presentMinutes;
        }
        dayItemInfoList = dayItemInfoList.stream().filter(item -> item.getBeginItemTime().compareTo(item.getEndItemTime()) != 0).collect(Collectors.toList());
        if (restStartTime == null) {
            for (DayItemInfoDTO dayItemInfoDTO : dayItemInfoList) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            return presentMinutes;
        }
        for (DayItemInfoDTO dayItemInfoDTO : dayItemInfoList) {
            if (dayItemInfoDTO.getEndItemTime().compareTo(restStartTime) < 1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) > -1 && dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) < 1) {
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) < 1 && dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                presentMinutes = presentMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) < 1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), restStartTime, DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(restEndTime, dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
        }
        return presentMinutes;
    }

}

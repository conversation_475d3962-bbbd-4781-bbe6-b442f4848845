package com.imile.attendance.abnormal.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.param.AbnormalAttendanceDayReminderParam;
import com.imile.attendance.abnormal.service.AttendanceAbnormalRemindService;
import com.imile.attendance.constants.BusinessConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 员工每日异常考勤提醒，每一个小时执行一次
 * @author: han.wang
 * @createDate: 2024-10-15
 * @version: 1.0
 */
@Slf4j
@Component
public class AbnormalAttendanceDayRemindHandler {

    @Autowired
    private AttendanceAbnormalRemindService attendanceAbnormalRemindService;

    @XxlJob(BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER)
    public ReturnT<String> abnormalAttendanceDayReminderHandler(String content) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}",
                BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER, content);
        AbnormalAttendanceDayReminderParam param;
        // 传参校验
        if (ObjectUtil.isNotEmpty(content)) {
            param = JSON.parseObject(content, AbnormalAttendanceDayReminderParam.class);
        } else {
            XxlJobLogger.log("XXL-JOB,  {} param not be null",
                    BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER);
            return ReturnT.FAIL;
        }
        // 设置发送方式为自动
        param.setSendType(BusinessConstant.N);
        // 发送异常考勤提醒
        attendanceAbnormalRemindService.sendRemind(param);

        return ReturnT.SUCCESS;
    }
}

package com.imile.attendance.abnormal.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-7-25
 * @version: 1.0
 */
@Data
public class AttendanceAbnormalDetailParam {

    /**
     * 异常考勤id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long abnormalId;
}

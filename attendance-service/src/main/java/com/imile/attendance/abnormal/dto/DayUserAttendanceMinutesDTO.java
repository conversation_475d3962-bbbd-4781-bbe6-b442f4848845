package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 当天用户的相关出勤时长
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
@Data
public class DayUserAttendanceMinutesDTO {

    /**
     * 请假时长(分钟)
     */
    private BigDecimal leaveMinutes;

    /**
     * 外勤时长(分钟)
     */
    private BigDecimal outOfOfficeMinutes;

    /**
     * 获取当天已出勤总时长
     */
    public BigDecimal usedTotalAttendanceMinutes() {
        return leaveMinutes.add(outOfOfficeMinutes);
    }
}

package com.imile.attendance.abnormal.service;

import com.imile.attendance.abnormal.param.AbnormalRemindRecordAddParam;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalRemindRecordQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22
 */
public interface AttendanceAbnormalRemindRecordService {

    /**
     * 异常发送记录查询
     *
     * @param query
     * @return
     */
    List<AttendanceAbnormalRemindRecordDO> listOnly(AbnormalRemindRecordQuery query);


    /**
     * 新增异常发送记录
     *
     * @param param
     * @return
     */
    void add(AbnormalRemindRecordAddParam param);
}

package com.imile.attendance.abnormal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 每日异常提醒模板参数
 * @author: han.wang
 * @createDate: 2024-10-15
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbnormalRemindTemplateDTO {
    /**
     * 入参:日期
     */
    private String abnormalDate;

    /**
     * 入参:异常类型
     */
    private String abnormalType;

    /**
     * 入参:出勤统计
     */
    private String statics;

    /**
     * 入参:考勤hr姓名
     */
    private String hrName;

    /**
     * 入参:考勤hr企业微信id
     */
    private String hrId;

    /**
     * 入参:任务id
     */
    private String taskId;

    /**
     * 入参:处理异常url(跳转clock考勤日历)
     */
    private String buttonUrl;

}

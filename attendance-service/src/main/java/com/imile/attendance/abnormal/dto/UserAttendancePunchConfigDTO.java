package com.imile.attendance.abnormal.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import lombok.Data;


/**
 * 当日考勤规则相关信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/28
 */
@Data
public class UserAttendancePunchConfigDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 考勤日期
     */
    private Long dayId;

    /**
     * 当天是真正的排班还是OFF/PH?
     */
    private Integer isActualPunch;

    /**
     * 当天的排班
     */
    private String dayPunchType;

    /**
     * 当天对应规则的班次
     */
    private PunchClassConfigDTO classConfigDO;
}

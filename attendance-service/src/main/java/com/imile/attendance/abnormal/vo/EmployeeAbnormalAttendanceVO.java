package com.imile.attendance.abnormal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EmployeeAbnormalAttendanceVO implements Serializable {
    private static final long serialVersionUID = -1901983984907227750L;

    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 系统账户名称
     */
    private String sysAccountName;
    /**
     * 邮件
     */
    private String email;
    /**
     * 员工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    private String employeeTypeDesc;

    /**
     * 异常类型
     */
    private String abnormalType;

    private String abnormalTypeDesc;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;

    private Long dayId;
    /**
     * 考勤异常日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 扫件数(司机页面使用)
     */
    private Integer scanCount;

    /**
     * 考勤日类型
     */
    private String attendanceType;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 异常数据状态
     */
    private String status;

    /**
     * 异常数据状态
     */
    private String statusDesc;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 工作网点名称
     */
    private String ocName;

    /**
     * 工作供应商ID
     */
    private Long vendorId;

    /**
     * 工作供应商名称
     */
    private String vendorName;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 职工类型
     */
    private String staffType;

    /**
     * 处理信息
     */
    private String processingInformation;

    /**
     * 用工形式
     */
    @WithDict(typeCode = "EmploymentForm", ref = "employmentFormDesc")
    private String employmentForm;

    /**
     * 用工形式描述
     */
    private String employmentFormDesc;
}

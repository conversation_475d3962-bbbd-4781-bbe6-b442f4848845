package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.util.Date;

/**
 * 当日考勤单据DTO
 *
 * <AUTHOR>
 * @since 2025/5/28
 */
@Data
public class DayAttendanceHandlerFormDTO {
    /**
     * 单据ID
     */
    private Long formId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 请假/外勤开始时间
     */
    private Date startTime;

    /**
     * 请假/外勤结束时间
     */
    private Date endTime;

    /**
     * 假期类型ID
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveType;

    /**
     * 请假/外勤开始时间 格式: yyyyMMdd
     */
    private Long startDayId;

    /**
     * 请假/外勤结束时间 格式: yyyyMMdd
     */
    private Long endDayId;
}

package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AbnormalAttendanceCountDTO implements Serializable {
    private static final long serialVersionUID = -6499625998173544788L;

    private Integer driverCount = 0;

    private Integer warehouseCount = 0;

    private Integer officeCount = 0;

    private Integer processCount = 0;

    private Integer processWarehouseCount;

    private Integer expiredWarehouseCount;
}

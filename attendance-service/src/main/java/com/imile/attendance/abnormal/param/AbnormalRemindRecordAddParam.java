package com.imile.attendance.abnormal.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 异常提醒发送记录传入对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbnormalRemindRecordAddParam implements Serializable {

    private static final long serialVersionUID = -2553563894220846339L;

    /**
     * 用户编码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private String userCode;

    /**
     * 异常日期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Long dayId;

    /**
     * 异常类型(异常主键拼接)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private String abnormalType;

    /**
     * 打卡班次id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Long punchClassConfigId;

    /**
     * 打卡班次时段id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private String punchClassItemConfigId;

    /**
     * 消息发送状态
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Integer sendStatus;

    /**
     * 消息类型(0:自动 1:手动)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Integer sendType;

    /**
     * 失败消息
     */
    private String sendMsg;

}

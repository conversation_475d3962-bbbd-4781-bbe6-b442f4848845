package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 当日考勤计算后班次时段时间DTO
 *
 * <AUTHOR>
 * @since 2025/5/26
 */
@Data
public class DayItemConfigDateDTO {

    /**
     * 上班时间
     */
    private Date punchInTime;

    /**
     * 下班时间
     */
    private Date punchOutTime;

    /**
     * 最早上班时间
     */
    private Date earliestPunchInTime;

    /**
     * 最晚上班时间
     */
    private Date latestPunchInTime;

    /**
     * 休息开始时间
     */
    private Date restStartTime;

    /**
     * 休息结束时间
     */
    private Date restEndTime;

    /**
     * 最晚下班时间
     */
    private Date latestPunchOutTime;

    /**
     * 总出勤要求时长
     */
    private BigDecimal itemTotalMinutes;

    /**
     * 弹性时长
     */
    private Long betweenMinutes = 0L;
}

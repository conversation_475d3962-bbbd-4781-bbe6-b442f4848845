package com.imile.attendance.abnormal.service.impl;

import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 免打卡考勤计算
 * 免打卡规则适用人员不需要进行考勤打卡，如果当天没有外勤或请假记录不生成员工出勤明细
 * 如果免打卡人员进行了外勤或请假操作，会消耗用户假期，考勤结果正常出,生成外勤或请假的员工出勤明细
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoNeedWorkCalculateServiceImpl")
public class NoNeedWorkCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {


    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 免打卡计算", calculateContext.getUser().getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();

        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);

        //当天的起始截止时间（截止时间用第二天的开始时间，如果用当天的结束时间23.59.59，会有1分钟的误差）
        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.beginOfDay(calculateHandlerDTO.getAttendanceTime()));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1));

        BigDecimal legalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = calculateUsedMinutes(calculateContext.getAttendanceEmployeeDetailDOList());

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = calculateContext.getAttendanceEmployeeDetailDOList().stream()
                .map(AttendanceEmployeeDetailDO::getFormId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        for (AttendanceFormDetailBO attendanceFormDetailBO : calculateContext.getUserPassFormBOList()) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                usedMinutes = outOffOfficeHandler(usedMinutes, totalMinutes, calculateContext.getAttendanceType(), null, calculateContext.getUser(), formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateHandlerDTO, addEmployeeDetailDOList);
                continue;
            }

            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                usedMinutes = leaveHandler(calculateContext.getUser(), usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateHandlerDTO, formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateContext.getUserLeaveDetailDOList(), calculateContext.getUserLeaveStageDetailDOList(),
                        calculateContext.getUserCompanyLeaveConfigDOList(), addEmployeeDetailDOList);
            }
        }

        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, null, updateAbnormalAttendanceDOList);
    }
}

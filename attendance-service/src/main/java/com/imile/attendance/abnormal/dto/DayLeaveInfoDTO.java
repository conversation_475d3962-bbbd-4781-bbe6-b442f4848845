package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-1
 * @version: 1.0
 */
@Data
public class DayLeaveInfoDTO {

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据ID
     */
    private Long applicationFormId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 请假开始时间
     */
    private Date leaveStartDate;

    /**
     * 请假结束时间
     */
    private Date leaveEndDate;

    /**
     * 用户预计请假时长
     */
    private String expectedLeaveTime;

    /**
     * 外勤开始时间
     */
    private Date outOfOfficeStartDate;

    /**
     * 外勤结束时间
     */
    private Date outOfOfficeEndDate;

    /**
     * 用户预计外勤时长
     */
    private String expectedOutOfOfficeTime;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人编码
     */
    private String createUserCode;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;
}

package com.imile.attendance.migration.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.migration.MigrationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description
 */
@Slf4j
@Service
public class MigrationServiceImpl implements MigrationService {

    @Value("${newAttendance.enable.countries:CHN}")
    private String enableNewAttendanceCountries;

    @Resource
    private AttendanceUserService userService;


    /**
     * 获取启用新考勤系统的国家列表
     */
    @Override
    public List<String> getEnableNewAttendanceCountry() {
        if (StringUtils.isEmpty(enableNewAttendanceCountries)) {
            log.error("启用新考勤系统的国家配置为空");
            return Collections.emptyList();
        }
        return Arrays.stream(enableNewAttendanceCountries.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前用户编码
     */
    @Override
    public String getCurrentUserCode() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (null == userContext) {
            log.debug("获取当前用户上下文为空");
            return null;
        }
        String userCode = userContext.getUserCode();
        log.debug("获取当前用户编码: {}", userCode);
        return userCode;
    }

    /**
     * 验证用户是否启用新考勤系统
     */
    @Override
    public Boolean verifyUserIsEnableNewAttendance() {
        // 1. 获取启用新考勤系统的国家列表
        List<String> enableNewAttendanceCountries = getEnableNewAttendanceCountry();
        if (CollectionUtils.isEmpty(enableNewAttendanceCountries)) {
            log.debug("启用新考勤系统的国家列表为空");
            return false;
        }

        // 2. 获取当前用户编码
        String currentUserCode = getCurrentUserCode();
        if (StringUtils.isBlank(currentUserCode)) {
            log.debug("当前用户编码为空");
            return false;
        }

        // 3. 查询用户信息（使用缓存）
        AttendanceUser attendanceUser = userService.getByUserCodeCache(currentUserCode);
        if (null == attendanceUser) {
            log.warn("用户信息不存在, userCode: {}", currentUserCode);
            return false;
        }

        // 4. 验证用户所在国家是否启用新考勤系统
        String userCountry = attendanceUser.getLocationCountry();
        boolean isEnabled = enableNewAttendanceCountries.contains(userCountry);

        log.info("用户新考勤系统转发, userCode: {}, country: {}, enabled: {}", currentUserCode, userCountry, isEnabled);
        return isEnabled;
    }
}
